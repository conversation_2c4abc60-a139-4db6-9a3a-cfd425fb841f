{"name": "stay-transit-backend", "version": "1.0.0", "description": "Stay Transit Backend", "main": "src/app.ts", "scripts": {"lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write \"src/**/*.{ts,js,json}\"", "format:check": "prettier --check \"src/**/*.{ts,js,json}\"", "build": "tsc", "start": "node -r ts-node/register --watch --env-file=.environments/.env.dev src/index.ts", "start:local": "node -r ts-node/register --env-file=.environments/.env.local src/index.ts", "start:prod": "node --env-file=.environments/.env.prod dist/index.js", "start:qa": "node --env-file=.environments/.env.qa dist/index.js", "precommit": "npm run lint:fix && npm run format", "prepare": "husky"}, "keywords": [], "author": "ITProFound", "license": "ITProFound", "devDependencies": {"@eslint/js": "9.19.0", "@types/axios": "0.14.4", "@types/bcryptjs": "2.4.6", "@types/cookie-parser": "1.4.9", "@types/cors": "2.8.17", "@types/express": "5.0.0", "@types/helmet": "4.0.0", "@types/jsonwebtoken": "9.0.8", "@types/mongoose": "5.11.97", "@types/multer": "1.4.13", "@types/node": "22.10.10", "@types/nodemailer": "6.4.17", "@types/swagger-jsdoc": "6.0.4", "@types/swagger-ui-express": "4.1.7", "@typescript-eslint/eslint-plugin": "8.21.0", "@typescript-eslint/parser": "8.21.0", "eslint": "9.35.0", "eslint-config-prettier": "10.0.1", "eslint-plugin-prettier": "5.2.3", "globals": "15.14.0", "husky": "9.1.7", "mongodb-memory-server": "10.1.3", "nodemon": "3.1.9", "prettier": "3.4.2", "ts-node": "10.9.2", "typescript": "5.7.3", "typescript-eslint": "8.21.0"}, "dependencies": {"@aws-sdk/client-s3": "3.828.0", "@aws-sdk/s3-request-presigner": "3.828.0", "axios": "1.12.2", "bcryptjs": "2.4.3", "body-parser": "1.20.3", "cookie-parser": "1.4.7", "cors": "2.8.5", "express": "4.21.2", "express-rate-limit": "8.1.0", "firebase-admin": "13.4.0", "helmet": "8.0.0", "jsonwebtoken": "9.0.2", "mongoose": "8.9.5", "multer": "2.0.2", "nanoid": "5.0.9", "nodemailer": "6.10.0", "stripe": "18.2.1", "swagger-jsdoc": "6.2.8", "swagger-ui-express": "5.0.1", "uuid": "11.1.0", "winston": "3.17.0", "winston-daily-rotate-file": "5.0.0", "zod": "4.1.0"}}