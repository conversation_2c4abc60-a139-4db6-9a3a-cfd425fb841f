config:
  target: 'http://localhost:3010'  # Replace with your API URL
  phases:
    - duration: 60  # Ramp-up phase (1 minute)
      arrivalRate: 10  # Start with 10 users/second
      rampTo: 50  # Ramp to 50 users/second
    - duration: 120  # Sustained load (2 minutes)
      arrivalRate: 50  # Maintain 50 users/second
  defaults:
    headers:
      Content-Type: 'application/json'

scenarios:
  - flow:
      - get:
          url: '/api/v1/properties/search?locationId=6891b406d3ab5c132fb81549&startDateTime=2025-09-08T19:45:24.000Z&endDateTime=2025-09-08T22:45:24.000Z&serviceTypeId=68b7cf0963cbbad0cfe51c4c'
          capture:
            json: '$.message'
            as: 'message'
      - log: 'Message: {{ message }}'
      - think: 1  # Pause 1 second between requests