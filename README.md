# Stay Transit Backend

This is the backend for the Stay Transit application, a comprehensive solution for managing transit accommodations. It is a Node.js application built with Express, TypeScript, and MongoDB.

## Table of Contents

- [Getting Started](#getting-started)
  - [Prerequisites](#prerequisites)
  - [Installation](#installation)
  - [Running the Application](#running-the-application)
- [Key Features](#key-features)
- [API Documentation](#api-documentation)
- [Linting and Formatting](#linting-and-formatting)
- [Building for Production](#building-for-production)
- [Docker](#docker)
- [Project Structure](#project-structure)
- [Dependencies](#dependencies)
- [License](#license)

## Getting Started

### Prerequisites

- Node.js (v22.14.0 or higher)
- npm

### Installation

1. Clone the repository:

2. Install the dependencies:
   ```bash
   npm install
   ```

### Running the Application

- **Development:**
  ```bash
  npm start
  ```
- **Production:**
  ```bash
  npm run start:prod
  ```
- **QA:**
  ```bash
  npm run start:qa
  ```

## Key Features

- **RESTful API:** A comprehensive set of endpoints for managing users, bookings, and properties.
- **Authentication and Authorization:** Secure user authentication and role-based access control using JWT.
- **Database Integration:** MongoDB with Mongoose for data modeling and persistence.
- **Payment Processing:** Integration with Stripe for secure payment handling.
- **Email Notifications:** Nodemailer for sending transactional emails.
- **File Storage:** AWS S3 for storing user-uploaded files.
- **Data Validation:** Zod for robust and type-safe data validation.

## Linting and Formatting

- **Lint:**
  ```bash
  npm run lint
  ```
- **Lint and Fix:**
  ```bash
  npm run lint:fix
  ```
- **Format:**
  ```bash
  npm run format
  ```
- **Check Format:**
  ```bash
  npm run format:check
  ```

## Building for Production

To build the application for production, run:

```bash
npm run build
```

This will compile the TypeScript code to JavaScript and output it to the `dist` directory.

## Docker

To build and run the Docker container, use the following commands:

```bash
docker build -t stay-transit-backend .
docker run -p 5000:5000 stay-transit-backend
```

## Project Structure

```
src
├── app.ts               # Express app configuration
├── config               # Configuration files
├── constants            # Constants
├── controllers          # Route handlers
├── index.ts             # Application entry point
├── middlewares          # Custom middleware
├── models               # Mongoose models
├── routes               # API routes
├── services             # Business logic
├── templates            # Email templates
├── types                # TypeScript types and interfaces
├── utils                # Utility functions
└── validators           # Zod validators
```

## Dependencies

### Main Dependencies

- **Express:** Web framework for Node.js
- **Mongoose:** MongoDB object modeling tool
- **TypeScript:** Superset of JavaScript that adds static types
- **Zod:** TypeScript-first schema validation
- **Stripe:** Payment processing
- **Nodemailer:** Email sending
- **Firebase Admin:** Firebase integration
- **AWS S3:** File storage

### Development Dependencies

- **ESLint:** Linter for JavaScript and TypeScript
- **Prettier:** Code formatter
- **Husky:** Git hooks

## License

ITProFound
