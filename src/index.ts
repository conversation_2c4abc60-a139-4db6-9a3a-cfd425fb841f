import app from './app';
import connectDB from './config/database';
import loggerService from './utils/logger/logger.service';
import { PORT } from './constants';

async function startServer() {
  await connectDB();
  app.listen(PORT, () => {
    loggerService.info(`Server is running on port ${PORT}`);
  });
}

startServer();

process.on('uncaughtException', async (error) => {
  const errorMessage = `Uncaught Exception: ${error.message}`;
  loggerService.error(errorMessage);
  process.exit(1);
});

process.on('unhandledRejection', async (reason) => {
  const errorMessage = `Unhandled Rejection: ${reason}`;
  loggerService.error(errorMessage);
  process.exit(1);
});
