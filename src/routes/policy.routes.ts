import { Router } from 'express';
import { PolicyController } from '../controllers/policy.controller';
import { validateBody } from '../middlewares/validators/validation.middleware';
import { policyValidationSchema } from '../validators/policy.validator';
import { authMiddleware } from '../middlewares/auth.middleware';
import { merchantMiddleware, superAdminMiddleware } from '../middlewares/rbac';

const router: Router = Router();

const policyController = PolicyController.getInstance();

router
  .route('/')
  .get(policyController.getAll)
  .post(authMiddleware, superAdminMiddleware, validateBody(policyValidationSchema), policyController.create);

router
  .route('/:id')
  .get(authMiddleware, merchantMiddleware, policyController.getById)
  .put(authMiddleware, superAdminMiddleware, validateBody(policyValidationSchema), policyController.update)
  .delete(authMiddleware, superAdminMiddleware, policyController.delete);

export default router;
