import { Router } from 'express';
import { PropertyPolicyController } from '../controllers/property-policy.controller';
import { validateBody } from '../middlewares/validators/validation.middleware';
import { createPropertyPolicySchema, updatePropertyPolicySchema } from '../validators/property-policy.validator';
import { authMiddleware } from '../middlewares/auth.middleware';
import { merchantMiddleware, superAdminMiddleware } from '../middlewares/rbac';

const router = Router({ mergeParams: true });
const propertyPolicyController = PropertyPolicyController.getInstance();

router
  .route('/')
  .get(propertyPolicyController.getAll)
  .post(authMiddleware, merchantMiddleware, validateBody(createPropertyPolicySchema), propertyPolicyController.create);

router
  .route('/:id')
  .get(propertyPolicyController.get)
  .put(authMiddleware, merchantMiddleware, validateBody(updatePropertyPolicySchema), propertyPolicyController.update)
  .delete(authMiddleware, superAdminMiddleware, propertyPolicyController.delete);

export default router;
