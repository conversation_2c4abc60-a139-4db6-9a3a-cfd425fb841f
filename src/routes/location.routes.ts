import { Router } from 'express';
import { LocationController } from '../controllers/location.controller';
import { validateBody } from '../middlewares/validators/validation.middleware';
import { createLocationSchema, updateLocationSchema } from '../validators/location.validator';
import { superAdminMiddleware } from '../middlewares/rbac';
import { authMiddleware } from '../middlewares/auth.middleware';

const router: Router = Router();

const locationController = LocationController.getInstance();

router
  .route('/')
  .get(locationController.getAll)
  .post(authMiddleware, superAdminMiddleware, validateBody(createLocationSchema), locationController.create);

router.route('/user').get(locationController.getUserLocationDetails);

router.route('/available').get(locationController.getAllAvailableLocations);

router
  .route('/:id')
  .get(locationController.get)
  .put(authMiddleware, superAdminMiddleware, validateBody(updateLocationSchema), locationController.update)
  .delete(authMiddleware, superAdminMiddleware, locationController.delete);

export default router;
