import { Router } from 'express';
import { BusinessDetailsController } from '../controllers/business-details.controller';
import { validateBody } from '../middlewares/validators/validation.middleware';
import { createBusinessDetailsSchema, updateBusinessDetailsSchema } from '../validators/business-details.validator';
import { merchantMiddleware, superAdminMiddleware } from '../middlewares/rbac';
import { authMiddleware } from '../middlewares/auth.middleware';

const router: Router = Router({ mergeParams: true });
const businessDetailsController = BusinessDetailsController.getInstance();

router
  .route('/')
  .get(businessDetailsController.getAll)
  .post(
    authMiddleware,
    merchantMiddleware,
    validateBody(createBusinessDetailsSchema),
    businessDetailsController.create,
  );

router
  .route('/:id')
  .get(businessDetailsController.get)
  .put(authMiddleware, merchantMiddleware, validateBody(updateBusinessDetailsSchema), businessDetailsController.update)
  .delete(authMiddleware, superAdminMiddleware, businessDetailsController.delete);

export default router;
