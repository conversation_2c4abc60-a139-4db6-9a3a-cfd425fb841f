import { Router } from 'express';
import { UserController } from '../controllers/user.controller';
import { authMiddleware } from '../middlewares/auth.middleware';
import { ReservationController } from '../controllers/reservation.controller';
import { validateBody } from '../middlewares/validators/validation.middleware';
import { userSchema, userUpdateSchema } from '../validators/user.validator';
import { superAdminMiddleware } from '../middlewares/rbac';

const router: Router = Router();
const userController = UserController.getInstance();
const reservationController = ReservationController.getInstance();

router.get('/', authMiddleware, superAdminMiddleware, userController.getAllUsers);
router.post('/', validateBody(userSchema), userController.register);
router.patch('/:userId', authMiddleware, validateBody(userUpdateSchema), userController.updateUser);
router.get('/reservations', authMiddleware, reservationController.getByUser);

export default router;
