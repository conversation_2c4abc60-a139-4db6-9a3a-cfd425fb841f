import { NextFunction, Request, Response, Router } from 'express';
import { UnitAvailabilityController } from '../controllers/unit-availability.controller';
import { roomAvailabilitySchema } from '../validators/unit-availability.validator';
import loggerService from '../utils/logger/logger.service';
import { ResponseUtil } from '../utils/response';

// Custom validation middleware for room availabilities
const validateRoomAvailabilities = (req: Request, res: Response, next: NextFunction): void => {
  try {
    const roomAvailabilities = req.body;
    const result = roomAvailabilitySchema.safeParse(roomAvailabilities);

    if (!result.success) {
      const errorMessage = result.error.issues[0]?.message || 'Invalid room availabilities data';
      loggerService.warn(`Room availabilities validation failed: ${errorMessage}`, 'unit-availability.routes.ts', {
        path: req.path,
        method: req.method,
        errors: result.error.issues,
      });
      ResponseUtil.badRequest(res, 'Invalid room availabilities data', errorMessage);
      return;
    }

    req.body.roomAvailabilities = result.data;
    next();
  } catch (error) {
    const errorMessage = (error as Error).message;
    loggerService.error('Room availabilities validation middleware error:', errorMessage);
    ResponseUtil.validationError(res, 'Validation failed', errorMessage);
  }
};

const router: Router = Router({ mergeParams: true });
const roomAvailabilityController = UnitAvailabilityController.getInstance();
router
  .route('/')
  .get(roomAvailabilityController.getByDate)
  .post(validateRoomAvailabilities, roomAvailabilityController.create);

export default router;
