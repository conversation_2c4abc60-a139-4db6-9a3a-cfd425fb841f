import { Router } from 'express';
import { FinanceController } from '../controllers/finance.controller';
import { validateBody } from '../middlewares/validators/validation.middleware';
import { createPayoutSchema } from '../validators/finance.validator';

const financeController = FinanceController.getInstance();

const router: Router = Router();
router.get('/', financeController.getDayWiseRevenue);
router
  .route('/payout')
  .post(validateBody(createPayoutSchema), financeController.createCompletedPayout)
  .get(financeController.getAllCompletedPayouts);

router.get('/consolidated-revenue', financeController.getConsolidatedRevenueByDates);
router.get('/net-payout-until', financeController.getNetPayoutUntilDate);

export default router;
