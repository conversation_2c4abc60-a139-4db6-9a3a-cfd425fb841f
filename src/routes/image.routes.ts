import { Router } from 'express';
import { upload } from '../middlewares/multer';
import { ImageController } from '../controllers/image.controller';
import { authMiddleware } from '../middlewares/auth.middleware';

const router: Router = Router();

const imageController = ImageController.getInstance();

router.post('/upload', authMiddleware, upload.single('image'), imageController.upload);
router.get('/preview/:key', imageController.preview);
// router.get('/download/:key', new ImageController().download());
// router.delete('/:key', new ImageController().delete());

export default router;
