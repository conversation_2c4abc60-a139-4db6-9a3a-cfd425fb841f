import { Router } from 'express';
import { DomainValueController } from '../controllers/domain-value.controller';
import { validateBody } from '../middlewares/validators/validation.middleware';
import { domainValueSchema } from '../validators/domain-value.validator';
import { merchantMiddleware } from '../middlewares/rbac';
import { authMiddleware } from '../middlewares/auth.middleware';

const router: Router = Router();

const domainValueController = DomainValueController.getInstance();

router
  .route('/:propertyId?')
  .get(domainValueController.getAll)
  .post(authMiddleware, merchantMiddleware, validateBody(domainValueSchema), domainValueController.create);

router
  .route('/:propertyId?/:id')
  .get(domainValueController.getById)
  .put(authMiddleware, merchantMiddleware, validateBody(domainValueSchema), domainValueController.update)
  .delete(authMiddleware, merchantMiddleware, domainValueController.delete);

export default router;
