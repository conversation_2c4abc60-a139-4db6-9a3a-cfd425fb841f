import { Router } from 'express';
import { PackageController } from '../controllers/package.controller';
import { getPackageCreateSchema, getPackageUpdateSchema } from '../validators/package.validator';
import { INextFunction, IRequest, IResponse } from '../types';
import { validateBodyByServiceType } from '../middlewares/validators/validate-by-service-type.middleware';
import { authMiddleware } from '../middlewares/auth.middleware';
import { merchantMiddleware } from '../middlewares/rbac';

export async function validatePackageCreateBody(req: IRequest, res: IResponse, next: INextFunction): Promise<void> {
  await validateBodyByServiceType(req, res, next, getPackageCreateSchema);
}

export async function validatePackageUpdateBody(req: IRequest, res: IResponse, next: INextFunction): Promise<void> {
  await validateBodyByServiceType(req, res, next, getPackageUpdateSchema);
}

const router: Router = Router({ mergeParams: true });

const packageController = PackageController.getInstance();

router
  .route('/')
  .get(packageController.getAll)
  .post(authMiddleware, merchantMiddleware, validatePackageCreateBody, packageController.create);

router
  .route('/:id')
  .get(packageController.get)
  .put(authMiddleware, merchantMiddleware, validatePackageUpdateBody, packageController.update)
  .delete(authMiddleware, merchantMiddleware, packageController.delete);

export default router;
