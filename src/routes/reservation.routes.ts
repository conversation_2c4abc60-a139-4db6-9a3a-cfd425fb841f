import { Router } from 'express';
import { ReservationController } from '../controllers/reservation.controller';
import { validateBody } from '../middlewares/validators/validation.middleware';
import {
  createReservationSchema,
  getOtpValidationSchema,
  updateValidationSchema,
  verifyOtpValidationSchema,
} from '../validators/reservation.validator';
import { authMiddleware } from '../middlewares/auth.middleware';
import { merchantMiddleware } from '../middlewares/rbac';

const router: Router = Router({ mergeParams: true });
const reservationController = ReservationController.getInstance();
router.get('/:propertyId/available', reservationController.getAvailable);
router.post('/block', validateBody(createReservationSchema), reservationController.block);

router.get('/property/:propertyId?', authMiddleware, merchantMiddleware, reservationController.getByPropertyId);

router.get('/:id', authMiddleware, reservationController.get);

router.get('/:reservationId/refundable-amount', authMiddleware, reservationController.getRefundableAmount);
router.post('/cancel/get-otp', authMiddleware, validateBody(getOtpValidationSchema), reservationController.getOtp);
router.post(
  '/cancel/verify-otp',
  authMiddleware,
  validateBody(verifyOtpValidationSchema),
  reservationController.verifyOtp,
);
router.patch(
  '/:id',
  authMiddleware,
  validateBody(updateValidationSchema),
  authMiddleware,
  reservationController.update,
);

export default router;
