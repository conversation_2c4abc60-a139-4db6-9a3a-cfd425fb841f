import { Router } from 'express';
import { PropertyDetailsController } from '../controllers/property-details.controller';
import { validateBody } from '../middlewares/validators/validation.middleware';
import {
  createPropertyAmenitiesSchema,
  createPropertyDetailsSchema,
  updatePropertyDetailsSchema,
} from '../validators/property-details.validator';
import { authMiddleware } from '../middlewares/auth.middleware';
import { merchantMiddleware, superAdminMiddleware } from '../middlewares/rbac';

const router: Router = Router({ mergeParams: true });
const propertyDetailsController = PropertyDetailsController.getInstance();

router
  .route('/')
  .get(propertyDetailsController.getAll)
  .post(
    authMiddleware,
    merchantMiddleware,
    validateBody(createPropertyDetailsSchema),
    propertyDetailsController.create,
  );

router.post(
  '/amenities',
  authMiddleware,
  merchantMiddleware,
  validateBody(createPropertyAmenitiesSchema),
  propertyDetailsController.createAmenities,
);

router
  .route('/:id')
  .get(propertyDetailsController.get)
  .put(authMiddleware, merchantMiddleware, validateBody(updatePropertyDetailsSchema), propertyDetailsController.update)
  .delete(authMiddleware, superAdminMiddleware, propertyDetailsController.delete);

export default router;
