import { Router } from 'express';
import { PaymentController } from '../controllers/payment.controller';
import { validateBody } from '../middlewares/validators/validation.middleware';
import { paymentSchema } from '../validators/payment.validator';
import { authMiddleware } from '../middlewares/auth.middleware';

const paymentController = PaymentController.getInstance();

const router: Router = Router();
router.route('/').post(validateBody(paymentSchema), paymentController.create);

router
  .route('/:id')
  .get(paymentController.get)
  // .put(validateBody(paymentSchema), paymentController.update)
  // .delete(paymentController.delete)
  .patch(paymentController.verifyPayment);

router.route('/:paymentProvider/checkout').post(paymentController.checkout);

router.post('/:reservationId/:paymentProvider/refund', authMiddleware, paymentController.refund);

export default router;
