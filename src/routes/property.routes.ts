import { Router } from 'express';
import { PropertyController } from '../controllers/property.controller';
import propertyDetailsRoutes from './property-details.routes';
import propertyPolicyRoutes from './property-policy.routes';
import businessDetailsRoutes from './business-details.routes';
import { validateBody } from '../middlewares/validators/validation.middleware';
import { patchPropertySchema, propertySchema } from '../validators/property.validator';
import { authMiddleware } from '../middlewares/auth.middleware';
import { superAdminMiddleware } from '../middlewares/rbac';

const router: Router = Router();

const propertyController = PropertyController.getInstance();

router.use('/:propertyId/property-details', propertyDetailsRoutes);
router.use('/:propertyId/business-details', businessDetailsRoutes);
router.use('/:propertyId/property-policies', propertyPolicyRoutes);

router
  .route('/')
  .get(authMiddleware, propertyController.getAll)
  .post(validateBody(propertySchema), authMiddleware, propertyController.create);

router.get('/search', propertyController.search);

router
  .route('/:id')
  .get(propertyController.get)
  .put(validateBody(propertySchema), authMiddleware, superAdminMiddleware, propertyController.update)
  .patch(authMiddleware, superAdminMiddleware, validateBody(patchPropertySchema), propertyController.updateStatus)
  .delete(authMiddleware, superAdminMiddleware, propertyController.delete);

router.get('/:id/has-data', authMiddleware, superAdminMiddleware, propertyController.hasData);

router.route('/:id/status').patch(authMiddleware, superAdminMiddleware, propertyController.patchStatus);

export default router;
