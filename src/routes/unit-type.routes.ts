import { Router } from 'express';
import { UnitTypeController } from '../controllers/unit-type.controller';
import unitTypeAvailabilityRoutes from './unit-availability.routes';
import { getUnitTypeCreateSchema, getUnitTypeUpdateSchema } from '../validators/unit-type.validator';
import { INextFunction, IRequest, IResponse } from '../types';
import { validateBodyByServiceType } from '../middlewares/validators/validate-by-service-type.middleware';
import { merchantMiddleware } from '../middlewares/rbac';
import { authMiddleware } from '../middlewares/auth.middleware';

export async function validateUnitTypeCreateBody(req: IRequest, res: IResponse, next: INextFunction): Promise<void> {
  await validateBodyByServiceType(req, res, next, getUnitTypeCreateSchema);
}

export async function validateUnitTypeUpdateBody(req: IRequest, res: IResponse, next: INextFunction): Promise<void> {
  await validateBodyByServiceType(req, res, next, getUnitTypeUpdateSchema);
}

const router: Router = Router({ mergeParams: true });
const roomTypeController = UnitTypeController.getInstance();

router.use('/availability', authMiddleware, merchantMiddleware, unitTypeAvailabilityRoutes);

router.route('/').get(roomTypeController.getAll).post(validateUnitTypeCreateBody, roomTypeController.create);

router
  .route('/:id')
  .get(roomTypeController.get)
  .put(validateUnitTypeUpdateBody, roomTypeController.update)
  .delete(roomTypeController.delete);
export default router;
