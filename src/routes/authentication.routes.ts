import { Router } from 'express';
import { AuthenticationController } from '../controllers/authentication.controller';
import { authMiddleware } from '../middlewares/auth.middleware';
import { validateBody } from '../middlewares/validators/validation.middleware';
import {
  changePasswordSchema,
  firebaseTokenSchema,
  forgotPasswordSchema,
  loginSchema,
  registerSchema,
  resetPasswordSchema,
  searchIfEmailExistsSchema,
} from '../validators/auth.validator';

const router: Router = Router();

const authenticationController = AuthenticationController.getInstance();

router.post('/register', validateBody(registerSchema), authenticationController.register);
router.post('/login', validateBody(loginSchema), authenticationController.login);
router.get('/profile', authMiddleware, authenticationController.profile);
router.post('/forgot-password', validateBody(forgotPasswordSchema), authenticationController.forgotPassword);
router.post('/reset-password', validateBody(resetPasswordSchema), authenticationController.resetPassword);
router.post(
  '/change-password',
  authMiddleware,
  validateBody(changePasswordSchema),
  authenticationController.changePassword,
);
router.post('/check-user', validateBody(searchIfEmailExistsSchema), authenticationController.checkUserExists);
router.post('/firebase', validateBody(firebaseTokenSchema), authenticationController.loginWithFirebase);
router.post('/logout', authenticationController.logout);
router.get('/verify-account', authenticationController.verifyAccount);

export default router;
