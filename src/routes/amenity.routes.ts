import { Router } from 'express';
import { AmenityController } from '../controllers/amenity.controller';
import { validateBody } from '../middlewares/validators/validation.middleware';
import { createAmenitySchema, updateAmenitySchema } from '../validators/amenity.validator';
import { superAdminMiddleware } from '../middlewares/rbac';
import { authMiddleware } from '../middlewares/auth.middleware';

const router: Router = Router();

const amenityController = AmenityController.getInstance();

router
  .route('/')
  .get(amenityController.getAll)
  .post(authMiddleware, superAdminMiddleware, validateBody(createAmenitySchema), amenityController.create);

router
  .route('/:id')
  .get(amenityController.get)
  .put(authMiddleware, superAdminMiddleware, validateBody(updateAmenitySchema), amenityController.update)
  .delete(authMiddleware, superAdminMiddleware, amenityController.delete);

export default router;
