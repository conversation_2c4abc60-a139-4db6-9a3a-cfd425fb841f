import { IReservation } from '../../types/models/reservation.types';
import { IProperty } from '../../types/models/property.types';
import { formatCurrency } from './utils';
import { DASHBOARD_FRONTEND_URL } from '../../constants';

import { IUser } from '../../types/models/user.types';
import { IPropertyCancellationPolicy } from '../../types/models/property-policies.types';

export const reservationTemplate = (
  firstName: string,
  reservation: IReservation,
  property: IProperty,
  cancellationPolicies: IPropertyCancellationPolicy[],
) => {
  const domainUrl = DASHBOARD_FRONTEND_URL;
  const updateUrl = `${domainUrl}/reservation/verify?type=update`;
  const cancelUrl = `${domainUrl}/reservation/verify?type=cancel`;

  const poc = property.owner as unknown as IUser;

  const totalCouponDiscount = reservation.items.reduce((acc, item) => acc + (item.couponDiscount || 0), 0);

  const address = [
    property.address.address1,
    property.address.address2,
    property.address.city,
    property.address.state,
    property.address.country,
    property.address.zipcode,
  ]
    .filter(Boolean)
    .join(', ')
    .replace(',,', ',');

  const mapUrl = `https://www.google.com/maps/search/?api=1&query=${property.address.latitude}%2C${property.address.longitude}&query_place_id=${property.address.placeId}`;

  let cancellationPoliciesHtml = '<div style="color: #888; font-size: 14px;">No cancellation policies listed.</div>';
  if (
    cancellationPolicies &&
    typeof cancellationPolicies === 'object' &&
    Object.keys(cancellationPolicies).length > 0
  ) {
    const items = cancellationPolicies
      .map((policy: IPropertyCancellationPolicy) => {
        return `<li>${policy.description}</li>`;
      })
      .filter(Boolean);
    if (items.length > 0) {
      cancellationPoliciesHtml = `<ul style="margin: 8px 0 0 18px; padding: 0; color: #444; font-size: 14px;">${items.join('')}</ul>`;
    }
  }

  return `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Reservation Confirmation - StayTransit</title>
            <style>
                body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f6f7fb; margin: 0; padding: 0; color: #222; }
                .header-bar { background: #fc4f4b; color: #fff; padding: 28px 0 18px 0; text-align: center; }
                .logo { display: inline-block; font-weight: bold; font-size: 28px; color: #fff; letter-spacing: 1px; margin-bottom: 6px; }
                .confirmation { font-size: 22px; font-weight: 700; margin-top: 8px; color: #fff; }
                .greeting { font-size: 15px; color: #fff; margin-block: 4px; }
                .main-content { max-width: 600px; margin: 0 auto; background: #fff; border-radius: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.04); padding: 32px 32px 0 32px; position: relative; top: -24px; }
                .section-title { font-size: 18px; font-weight: 700; margin: 24px 0 12px 0; color: #222; }
                .booking-id { font-size: 13px; color: #888; margin-bottom: 8px; }
                .property-name { font-size: 16px; font-weight: 600; color: #fc4f4b; margin-bottom: 2px; }
                .property-address { font-size: 15px; color: #222; margin-bottom: 4px; word-break: break-word; }
                .directions-link { color: #fc4f4b; font-size: 14px; text-decoration: underline; margin-bottom: 10px; display: inline-block; }
                .help-links { font-size: 13px; color: #555; margin-bottom: 10px; }
                .help-links a { color: #fc4f4b; text-decoration: underline; margin-left: 8px; }
                .details-list { margin: 0 0 12px 0; padding: 0; list-style: none; }
                .details-list li { margin-bottom: 6px; font-size: 15px; }
                .details-action { color: #fc4f4b; text-decoration: underline; font-size: 14px; margin-left: 8px; cursor: pointer; }
                .support-section { background: #f6f7fb; border-radius: 8px; padding: 16px; margin: 24px 0; display: flex; align-items: center; justify-content: space-between; }
                .support-title { font-size: 15px; font-weight: 600; }
                .support-btn { background: #fc4f4b; color: #fff; border: none; border-radius: 6px; padding: 8px 18px; font-size: 14px; font-weight: 600; cursor: pointer; }
                .payment-table { width: 100%; border-collapse: collapse; margin: 18px 0; }
                .payment-table td { padding: 10px 0; font-size: 15px; border-bottom: 1px solid #f0f0f0; }
                .payment-table .label { color: #555; }
                .payment-table .total { font-weight: bold; color: #222; font-size: 17px; border-top: 2px solid #fc4f4b; }
                .pay-btn { background: #fc4f4b; color: #fff; border: none; border-radius: 6px; padding: 10px 28px; font-size: 16px; font-weight: 700; cursor: pointer; margin-top: 10px; }
                .cancel-section { background: #f6f7fb; border-radius: 8px; padding: 18px 20px; margin: 24px 0; }
                .cancel-title { font-size: 15px; font-weight: 700; color: #FF715D; margin-bottom: 6px; }
                .cancel-policy { font-size: 14px; color: #555; margin-bottom: 8px; }
                .cancel-btn { background: #fff; border: 1.5px solid #fc4f4b; color: #fc4f4b; padding: 10px 24px; border-radius: 8px; font-size: 15px; font-weight: 700; cursor: pointer; margin-top: 10px; display: inline-block; }
                .rules-section { margin: 24px 0; }
                .rules-title { font-size: 15px; font-weight: 700; color: #222; margin-bottom: 8px; }
                .rules-list { margin: 0 0 0 18px; padding: 0; color: #444; font-size: 14px; }
                .cancellation-policies { margin: 12px 0 0 18px; padding: 0; color: #444; font-size: 14px; line-height: 1.6;}
                .cancellation-policies li { margin-bottom: 8px; list-style-type: disc; }
                @media (max-width: 700px) {
                    .main-content { padding: 12px !important; }
                }
            </style>
        </head>
        <body>
            <div class="header-bar">
                <div class="logo">StayTransit</div>
                <div class="confirmation">Your booking is confirmed.</div>
                <div class="greeting">Hello ${firstName}, Thank you for choosing StayTransit.</div>
            </div>
            <div class="main-content">
                <div class="section-title">Booking details <span class="booking-id">(ID: ${reservation.reservationCode})</span></div>
                <div class="property-name">${property.name}</div>
                <div class="property-address">${address}</div>
                <a class="directions-link" href="${mapUrl}">Get directions</a>
                <!-- End of booking details block -->
                <div class="help-links">
                    For special requests or hotel amenities, early check-in, call <a href="tel:${poc.phone.countryCode}${poc.phone.phoneNumber}">${poc.phone.countryCode} ${poc.phone.phoneNumber}</a>
                </div>
                <ul class="details-list">
                    <li>Guests: ${reservation.items.reduce((acc, item) => acc + item.noOfAdults, 0)} Adult${reservation.items.some((item) => item.noOfChildren > 0) ? `, ${reservation.items.reduce((acc, item) => acc + item.noOfChildren, 0)} Children` : ''} <a href="${updateUrl}"><span class="details-action">Update guest</span></a></li>
                    <li>Rooms: ${reservation.items.length} Room${reservation.items.length > 1 ? 's' : ''}</li>
                </ul>
                <div class="section-title">Payment summary</div>
                <table class="payment-table">
                    <tr>
                        <td class="label">Room Charges</td>
                        <td>Room price for 1 Room x ${reservation.items.length || 1} guest</td>
                        <td style="text-align:right;">${formatCurrency(reservation.subTotal)}</td>
                    </tr>
                    <tr>
                        <td class="label">Coupon Discount</td>
                        <td></td>
                        <td style="text-align:right;">${formatCurrency(totalCouponDiscount)}</td>
                    </tr>
                    <tr>
                        <td class="label">Taxes & Charges</td>
                        <td></td>
                        <td style="text-align:right;">${formatCurrency(reservation.totalTax)}</td>
                    </tr>
                    <tr>
                        <td class="label total">Total Paid</td>
                        <td></td>
                        <td class="total" style="text-align:right;">${formatCurrency(reservation.grandTotal)}</td>
                    </tr>
                </table>
                <div class="cancel-section">
                    <div class="cancel-title">&#9888; Change in plans? No problem</div>
                    <a href="${cancelUrl}"><button class="cancel-btn">Cancel booking</button></a>
                </div>
                <div class="rules-section">
                    <div class="rules-title">Cancellation Rules</div>
                    ${cancellationPoliciesHtml.replace('<ul', '<ul class="cancellation-policies"')}
                </div>
            </div>
        </body>
        </html>
    `;
};
