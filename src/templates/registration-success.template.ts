import { IUser } from '../types';
import { DASHBOARD_FRONTEND_URL } from '../constants';

export const verifyMerchantAccountTemplate = (merchant: IUser, verificationToken: string) => {
  const verificationUrl = `${DASHBOARD_FRONTEND_URL}?redirect=onboarding&token=${verificationToken}`;

  return `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Verify Your Merchant Account</title>
  <style>
    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f6f7fb; margin: 0; padding: 0; color: #222; }
    .header-bar { background: #fc4f4b; color: #fff; padding: 28px 0 18px 0; text-align: center; }
    .logo { display: inline-block; font-weight: bold; font-size: 28px; color: #fff; letter-spacing: 1px; margin-bottom: 6px; }
    .confirmation { font-size: 22px; font-weight: 700; margin-top: 8px; color: #fff; }
    .greeting { font-size: 15px; color: #fff; margin-top: 4px; }
    .main-content { max-width: 600px; margin: 0 auto; background: #fff; border-radius: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.04); padding: 32px 32px 0 32px; position: relative; top: -24px; }
    .section-title { font-size: 18px; font-weight: 700; margin: 24px 0 12px 0; color: #222; }
    .details { background: #f6f7fb; border-radius: 8px; padding: 16px; margin: 24px 0; }
    .details p { margin: 5px 0; }
    .details strong { color: #fc4f4b; }
    .button { background: #fc4f4b; text-decoration: none; color: #fff; border: none; border-radius: 6px; padding: 10px 24px; font-size: 15px; font-weight: 700; cursor: pointer; margin-top: 10px; display: inline-block; text-decoration: none; }
    .button:hover { background: #fc4f4b; }
    .footer { background: #f6f7fb; padding: 15px; text-align: center; font-size: 12px; color: #666; border-top: 1px solid #eee; }
    .footer a { color: #fc4f4b; text-decoration: none; }
    @media (max-width: 700px) {
      .main-content { padding: 12px !important; }
    }
  </style>
</head>
<body>
  <div class="header-bar">
    <div class="logo">StayTransit</div>
    <div class="confirmation">Verify Your Merchant Account</div>
    <div class="greeting">Hello ${merchant.firstName || 'Merchant'}, Welcome to StayTransit!</div>
  </div>
  <div class="main-content">
    <div class="section-title">Account Verification</div>
    <div class="details">
      <p><strong>Merchant Information:</strong></p>
      <p>&bull; Name: ${merchant.firstName || 'N/A'} ${merchant.lastName || ''}</p>
      <p>&bull; Email: ${merchant.email || 'N/A'}</p>
    </div>
    <p>To activate your merchant account and start managing your bookings, please verify your account by clicking the button below.</p>
    <a href="${verificationUrl}" class="button">Verify Your Account</a>
    <p>If the button doesn't work, copy and paste this link into your browser:</p>
    <p><a href="${verificationUrl}">${verificationUrl}</a></p>
    <p>For any questions, our support team is here to assist you.<br>Best regards,<br>StayTransit Team</p>
    <div class="footer">
      © 2025 StayTransit. All rights reserved. <a href="https://staytransit.com/support">Contact Support</a> | <a href="https://staytransit.com/privacy">Privacy Policy</a>
    </div>
  </div>
</body>
</html>
  `;
};

export const accountVerifiedSuccessfullyTemplate = (merchant: IUser) => {
  return `
    <!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Merchant Account Created</title>
  <style>
    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f6f7fb; margin: 0; padding: 0; color: #222; }
    .header-bar { background: #fc4f4b; color: #fff; padding: 28px 0 18px 0; text-align: center; }
    .logo { display: inline-block; font-weight: bold; font-size: 28px; color: #fff; letter-spacing: 1px; margin-bottom: 6px; }
    .confirmation { font-size: 22px; font-weight: 700; margin-top: 8px; color: #fff; }
    .greeting { font-size: 15px; color: #fff; margin-top: 4px; }
    .main-content { max-width: 600px; margin: 0 auto; background: #fff; border-radius: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.04); padding: 32px 32px 0 32px; position: relative; top: -24px; }
    .section-title { font-size: 18px; font-weight: 700; margin: 24px 0 12px 0; color: #222; }
    .details { background: #f6f7fb; border-radius: 8px; padding: 16px; margin: 24px 0; }
    .details p { margin: 5px 0; }
    .details strong { color: #fc4f4b; }
    .button { background: #fc4f4b; text-decoration: none; color: #fff; border: none; border-radius: 6px; padding: 10px 24px; font-size: 15px; font-weight: 700; cursor: pointer; margin-top: 10px; display: inline-block; text-decoration: none; }
    .button:hover { background: #fc4f4b; }
    .footer { background: #f6f7fb; padding: 15px; text-align: center; font-size: 12px; color: #666; border-top: 1px solid #eee; }
    .footer a { color: #fc4f4b; text-decoration: none; }
    @media (max-width: 700px) {
      .main-content { padding: 12px !important; }
    }
  </style>
</head>
<body>
  <div class="header-bar">
    <div class="logo">StayTransit</div>
    <div class="confirmation">Your Merchant Account is Ready!</div>
    <div class="greeting">Hello ${merchant.firstName || 'Merchant'}, Welcome to the StayTransit Partner Network!</div>
  </div>
  <div class="main-content">
    <div class="section-title">Account Creation Confirmed</div>
    <div class="details">
      <p><strong>Merchant Information:</strong></p>
      <p>&bull; Name: ${merchant.firstName || 'N/A'} ${merchant.lastName || ''}</p>
      <p>&bull; Email: ${merchant.email || 'N/A'}</p>
    </div>
    <p>Congratulations! Your merchant account has been successfully verified and created. You can now start managing your bookings and properties through the StayTransit Partner Panel.</p>
    <a href="${DASHBOARD_FRONTEND_URL}/partner-panel" class="button">Access Partner Panel</a>
    <p>If the button doesn't work, copy and paste this link into your browser:</p>
    <p><a href="${DASHBOARD_FRONTEND_URL}/partner-panel">${DASHBOARD_FRONTEND_URL}/partner-panel</a></p>
    <p>We're excited to have you on board! For any questions, our support team is here to assist you.<br>Best regards,<br>StayTransit Team</p>
    <div class="footer">
      © 2025 StayTransit. All rights reserved. <a href="https://staytransit.com/support">Contact Support</a> | <a href="https://staytransit.com/privacy">Privacy Policy</a>
    </div>
  </div>
</body>
</html>
`;
};
