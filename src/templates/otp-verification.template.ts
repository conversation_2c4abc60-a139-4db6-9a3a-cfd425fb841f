export const otpVerificationTemplate = (firstName: string, otp: string) => {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <style>
        body {
          font-family: Arial, sans-serif;
          background-color: #f4f4f4;
          margin: 0;
          padding: 0;
          color: #333;
        }
        .container {
          max-width: 600px;
          margin: 20px auto;
          background-color: #ffffff;
          border-radius: 8px;
          overflow: hidden;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .header {
          background-color: #FC4F4B;
          padding: 20px;
          text-align: center;
        }
        .header h1 {
          color: #ffffff;
          margin: 0;
          font-size: 24px;
        }
        .content {
          padding: 30px;
          text-align: center;
        }
        .content p {
          font-size: 16px;
          line-height: 1.6;
          margin: 10px 0;
        }
        .otp {
          font-size: 24px;
          font-weight: bold;
          color: #FC4F4B;
          margin: 20px 0;
          letter-spacing: 2px;
        }
        .button:hover {
          background-color: #e0433f;
        }
        .footer {
          background-color: #f4f4f4;
          padding: 20px;
          text-align: center;
          font-size: 14px;
          color: #666;
        }
        @media only screen and (max-width: 600px) {
          .container {
            margin: 10px;
          }
          .content {
            padding: 20px;
          }
          .header h1 {
            font-size: 20px;
          }
          .otp {
            font-size: 20px;
          }
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Booking Verification, ${firstName}!</h1>
        </div>
        <div class="content">
          <p>We need to verify that this booking belongs to you for your StayTransit account.</p>
          <p>Please use the following OTP to confirm your booking:</p>
          <p class="otp">${otp}</p>
          <p>If you did not initiate this booking, please ignore this email or contact our support team.</p>
          <p>Thank you for choosing StayTransit!</p>
        </div>
        <div class="footer">
          <p>&copy; ${new Date().getFullYear()} StayTransit. All rights reserved.</p>
          <p>Contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
        </div>
      </div>
    </body>
    </html>
    `;
};
