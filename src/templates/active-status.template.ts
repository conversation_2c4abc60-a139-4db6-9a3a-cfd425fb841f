import { DASHBOARD_FRONTEND_URL } from '../constants';

export const activeStatusTemplate = (firstName: string, isActive: boolean) => {
  if (isActive) {
    return `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Your StayTransit Account Is Now Active</title>
                <style>
                    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
                    .container { max-width: 650px; margin: 20px auto; background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
                    .header { background: linear-gradient(135deg, #fc4f4b 0%, #c61316 100%); color: white; padding: 30px; text-align: center; }
                    .header h1 { margin: 0; font-size: 28px; font-weight: 600; }
                    .header p { margin: 10px 0 0 0; opacity: 0.9; }
                    .content { padding: 40px; }
                    .section { background: white; padding: 25px; margin: 25px 0; border-radius: 10px; border-left: 5px solid; box-shadow: 0 2px 4px rgba(0,0,0,0.05); }
                    .next-steps { border-left-color: #fc4f4b; }
                    .footer { text-align: center; padding: 30px; background: #c61316; color: white; }
                    .cta-button { display: inline-block; background: #fc4f4b; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; margin: 10px 0; }
                    .highlight { background: #d5f4e6; padding: 4px 8px; border-radius: 4px; color: #27ae60; font-weight: bold; }
                    @media (max-width: 600px) {
                        .container { margin: 10px; }
                        .content { padding: 20px; }
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>✅ Account Activated</h1>
                        <p>Your StayTransit Account Is Now Active</p>
                    </div>
                    
                    <div class="content">
                        <p>Dear <strong>${firstName}</strong>,</p>
                        
                        <p>Your StayTransit account is now active. You can log in to your dashboard, manage your property, and begin receiving guest bookings (if packages are added and approved).</p>
                        
                        <div class="section next-steps">
                            <h3>📋 Next Steps</h3>
                            <p>Thank you for choosing StayTransit. We look forward to supporting your success.</p>
                            
                            <div style="text-align: center; margin: 20px 0;">
                                <a href="${DASHBOARD_FRONTEND_URL}" class="cta-button">
                                    Access Dashboard
                                </a>
                            </div>
                        </div>
                        
                        <p style="margin-top: 30px;">
                            <strong>Best regards,</strong><br>
                            StayTransit Team
                        </p>
                    </div>
                    
                    <div class="footer">
                        <p>© 2024 StayTransit. All rights reserved.</p>
                    </div>
                </div>
            </body>
            </html>
        `;
  } else {
    return `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Your StayTransit Account Has Been Deactivated</title>
                <style>
                    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
                    .container { max-width: 650px; margin: 20px auto; background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
                    .header { background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%); color: white; padding: 30px; text-align: center; }
                    .header h1 { margin: 0; font-size: 28px; font-weight: 600; }
                    .header p { margin: 10px 0 0 0; opacity: 0.9; }
                    .content { padding: 40px; }
                    .section { background: white; padding: 25px; margin: 25px 0; border-radius: 10px; border-left: 5px solid; box-shadow: 0 2px 4px rgba(0,0,0,0.05); }
                    .next-steps { border-left-color: #3498db; }
                    .contact-info { border-left-color: #f39c12; }
                    .footer { text-align: center; padding: 30px; background: #2c3e50; color: white; }
                    .cta-button { display: inline-block; background: #3498db; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; margin: 10px 0; }
                    .highlight { background: #ecf0f1; padding: 4px 8px; border-radius: 4px; color: #95a5a6; font-weight: bold; }
                    @media (max-width: 600px) {
                        .container { margin: 10px; }
                        .content { padding: 20px; }
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>⏸️ Account Deactivated</h1>
                        <p>Your StayTransit Account Has Been Deactivated</p>
                    </div>
                    
                    <div class="content">
                        <p>Dear <strong>${firstName}</strong>,</p>
                        
                        <p>Your StayTransit account has been deactivated. This action may have been taken due to inactivity, policy violation, or at your request.</p>
                        
                        <div class="section next-steps">
                            <h3>📋 Next Steps</h3>
                            <p>If you believe this was done in error or would like to request reactivation, please contact our support team at <strong><EMAIL></strong>.</p>
                        </div>
                        
                        <p style="margin-top: 30px;">
                            <strong>Best regards,</strong><br>
                            StayTransit Team
                        </p>
                    </div>
                    
                    <div class="footer">
                        <p>© 2024 StayTransit. All rights reserved.</p>
                    </div>
                </div>
            </body>
            </html>
        `;
  }
};
