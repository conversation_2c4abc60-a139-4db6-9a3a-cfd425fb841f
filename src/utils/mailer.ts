import nodemailer from 'nodemailer';
import loggerService from './logger/logger.service';
import {
  ERROR_EMAIL_RECIPIENT,
  MAIL_FROM,
  MAIL_IS_SECURE,
  <PERSON>IL_PASSWORD,
  <PERSON><PERSON>_SMTP_HOST,
  <PERSON>IL_SMTP_PORT,
  MAIL_USERNAME,
} from '../constants';

export const sendErrorEmail = async (subject: string, errorMessage: string): Promise<void> => {
  try {
    const transporter = nodemailer.createTransport({
      host: MAIL_SMTP_HOST,
      port: MAIL_SMTP_PORT,
      requireTLS: true,
      secure: MAIL_IS_SECURE,
      auth: {
        user: MAIL_USERNAME,
        pass: MAIL_PASSWORD,
      },
    });
    const nodeMailerTransport = {
      from: MAIL_FROM,
      to: ERROR_EMAIL_RECIPIENT,
      subject: subject,
      html: errorMessage,
    };
    await transporter.sendMail(nodeMailerTransport);
    loggerService.info(`Error email sent: ${subject}`);
  } catch (error) {
    loggerService.error(`Failed to send error email: `, 'mailer.ts', error);
  }
};
