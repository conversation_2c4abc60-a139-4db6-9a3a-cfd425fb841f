import winston from 'winston';
import 'winston-daily-rotate-file';
import fs from 'fs';
import path from 'path';
import {
  combinedLogFile,
  errorLogFile,
  logDirectory,
  logFormat,
  logLevels,
  maxLogFiles,
  maxLogFileSize,
} from './logger.config';
import { NODE_ENV } from '../../constants';

class LoggerService {
  private static instance: LoggerService;
  private logger: winston.Logger;

  private constructor() {
    if (!fs.existsSync(logDirectory)) {
      fs.mkdirSync(logDirectory);
    }

    const transports: winston.transport[] = [
      new winston.transports.Console({
        level: NODE_ENV === 'production' ? 'warn' : 'debug',
        format: logFormat,
      }),

      new winston.transports.DailyRotateFile({
        filename: path.join(logDirectory, errorLogFile),
        level: 'error',
        format: winston.format.combine(
          winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
          winston.format.json(),
        ),
        datePattern: 'YYYY-MM-DD',
        maxSize: maxLogFileSize,
        maxFiles: maxLogFiles,
      }),

      new winston.transports.DailyRotateFile({
        filename: path.join(logDirectory, combinedLogFile),
        format: winston.format.combine(
          winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
          winston.format.json(),
        ),
        datePattern: 'YYYY-MM-DD',
        maxSize: maxLogFileSize,
        maxFiles: maxLogFiles,
      }),
    ];

    this.logger = winston.createLogger({
      levels: logLevels,
      format: logFormat,
      transports,
      exitOnError: false,
    });
  }

  public static getInstance(): LoggerService {
    if (!LoggerService.instance) {
      LoggerService.instance = new LoggerService();
    }
    return LoggerService.instance;
  }

  public error(message: string, module?: string, meta?: unknown): void {
    const metaData = meta && typeof meta === 'object' ? meta : {};
    this.logger.error(message, { module, ...metaData });
  }

  public warn(message: string, module?: string, meta?: unknown): void {
    const metaData = meta && typeof meta === 'object' ? meta : {};
    this.logger.warn(message, { module, ...metaData });
  }

  public info(message: string, module?: string, meta?: unknown): void {
    const metaData = meta && typeof meta === 'object' ? meta : {};
    this.logger.info(message, { module, ...metaData });
  }

  public http(message: string, module?: string, meta?: unknown): void {
    const metaData = meta && typeof meta === 'object' ? meta : {};
    this.logger.http(message, { module, ...metaData });
  }

  public debug(message: string, module?: string, meta?: unknown): void {
    const metaData = meta && typeof meta === 'object' ? meta : {};
    this.logger.debug(message, { module, ...metaData });
  }

  public log(level: string, message: string, module?: string, meta?: unknown): void {
    const metaData = meta && typeof meta === 'object' ? meta : {};
    this.logger.log(level, message, { module, ...metaData });
  }
}

export default LoggerService.getInstance();
