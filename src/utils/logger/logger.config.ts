import { format } from 'winston';

export const logLevels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
} as const;

export const logColors: Record<keyof typeof logLevels, string> = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'blue',
};

export const logFormat = format.combine(
  format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
  format.printf((info) => {
    const level = info.level as keyof typeof logColors;
    const levelWithColor = `\x1b[${getColorCode(logColors[level])}m${level.toUpperCase()}\x1b[0m`;

    let log = `[${info.timestamp}] [${levelWithColor}]`;

    if (info.module && typeof info.module === 'string') {
      log += ` [\x1b[${getColorCode('cyan')}m${info.module}\x1b[0m]`;
    }

    log += `: ${info.message}`;

    return log;
  }),
);

function getColorCode(color: string): string {
  const colorCodes: Record<string, string> = {
    red: '31',
    yellow: '33',
    green: '32',
    magenta: '35',
    blue: '34',
    cyan: '36',
  };
  return colorCodes[color] || '0';
}

export const logDirectory = 'logs';
export const errorLogFile = 'error.log';
export const combinedLogFile = 'combined.log';
export const maxLogFileSize = '10m';
export const maxLogFiles = 5;
