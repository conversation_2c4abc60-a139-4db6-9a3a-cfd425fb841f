import loggerService from './logger/logger.service';
import RateCardSchema from '../models/rate-card.model';
import mongoose from 'mongoose';

interface RateCardQueryParams {
  packageIds: string[];
  startDateTime: string | Date;
  endDateTime: string | Date;
}

function parseDates(
  startDateTime: string | Date,
  endDateTime: string | Date,
): { startDate: Date; endDate: Date } | null {
  const startDate = new Date(startDateTime);
  const endDate = new Date(endDateTime);
  if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
    loggerService.warn('Invalid startDateTime or endDateTime provided');
    return null;
  }
  return { startDate, endDate };
}

export async function getRateCardPrices({
  packageIds,
  startDateTime,
  endDateTime,
}: RateCardQueryParams): Promise<Record<string, number>> {
  if (!Array.isArray(packageIds) || packageIds.length === 0) {
    loggerService.warn('Invalid or empty packageIds provided');
    return {};
  }

  const dates = parseDates(startDateTime, endDateTime);
  if (!dates) return {};

  const { startDate, endDate } = dates;
  if (startDate > endDate) {
    loggerService.warn('startDateTime is after endDateTime');
    return {};
  }

  try {
    const rateCards = await RateCardSchema.aggregate([
      {
        $match: {
          packageId: { $in: packageIds.map((id) => new mongoose.Types.ObjectId(id)) },
          dateTime: {
            $gte: startDate,
            $lte: endDate,
          },
        },
      },
      {
        $group: {
          _id: '$packageId',
          price: { $max: '$price' },
        },
      },
      {
        $project: {
          _id: 0,
          packageId: '$_id',
          price: 1,
        },
      },
    ]);

    return rateCards.reduce(
      (acc, { packageId, price }) => {
        acc[packageId] = price;
        return acc;
      },
      {} as Record<string, number>,
    );
  } catch (error) {
    loggerService.error(
      `Failed to fetch rate cards for packageIds: ${packageIds.join(', ')}, startDate: ${startDateTime}, endDate: ${endDateTime}`,
      'rate-card.ts',
      error,
    );
    throw new Error('Unable to fetch rate card prices');
  }
}
