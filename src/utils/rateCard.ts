import loggerService from './logger/logger.service';
import RateCardSchema from '../models/rate-card.model';
import PackageModel from '../models/package.model';
import mongoose from 'mongoose';

interface RateCardQueryParams {
  packageIds: string[];
  startDateTime: string | Date;
  endDateTime: string | Date;
}

function parseDates(
  startDateTime: string | Date,
  endDateTime: string | Date,
): { startDate: Date; endDate: Date } | null {
  const startDate = new Date(startDateTime);
  const endDate = new Date(endDateTime);
  if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
    loggerService.warn('Invalid startDateTime or endDateTime provided');
    return null;
  }
  return { startDate, endDate };
}

export async function getRateCardPrices({
  packageIds,
  startDateTime,
  endDateTime,
}: RateCardQueryParams): Promise<Record<string, number>> {
  if (!Array.isArray(packageIds) || packageIds.length === 0) {
    loggerService.warn('Invalid or empty packageIds provided');
    return {};
  }

  const dates = parseDates(startDateTime, endDateTime);
  if (!dates) return {};

  const { startDate, endDate } = dates;
  if (startDate > endDate) {
    loggerService.warn('startDateTime is after endDateTime');
    return {};
  }

  try {
    // Get packages to get base prices
    const packages = await PackageModel.find({
      _id: { $in: packageIds.map((id) => new mongoose.Types.ObjectId(id)) }
    }).select('_id price');

    const packagePriceMap = packages.reduce((acc, pkg) => {
      acc[pkg._id.toString()] = pkg.price;
      return acc;
    }, {} as Record<string, number>);

    // Get rate card rules for the date range
    const dayNames = [];
    const currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      const dayName = currentDate.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
      if (!dayNames.includes(dayName)) {
        dayNames.push(dayName);
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }

    const rateCards = await RateCardSchema.find({
      packageId: { $in: packageIds.map((id) => new mongoose.Types.ObjectId(id)) },
      isActive: true,
      fromDate: { $lte: endDate },
      toDate: { $gte: startDate },
      $or: [
        { days: 'all' },
        { days: { $in: dayNames } }
      ]
    }).populate('packageId', 'price');

    // Calculate adjusted prices for each package
    const result: Record<string, number> = {};

    for (const packageId of packageIds) {
      const basePrice = packagePriceMap[packageId] || 0;

      // Find the most recent applicable rate card rule
      const applicableRules = rateCards.filter(rule =>
        rule.packageId._id.toString() === packageId
      ).sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

      if (applicableRules.length > 0) {
        const rule = applicableRules[0];
        const adjustedPrice = basePrice * (1 + rule.percentageChange / 100);
        result[packageId] = Math.max(0, adjustedPrice); // Ensure price is not negative
      } else {
        result[packageId] = basePrice;
      }
    }

    return result;
  } catch (error) {
    loggerService.error(
      `Failed to fetch rate cards for packageIds: ${packageIds.join(', ')}, startDate: ${startDateTime}, endDate: ${endDateTime}`,
      'rate-card.ts',
      error,
    );
    throw new Error('Unable to fetch rate card prices');
  }
}
