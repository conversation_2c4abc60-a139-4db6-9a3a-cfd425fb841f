import { IResponse } from '../types';

/**
 * Standard API response interface
 */
export interface ApiResponse<T = unknown> {
  success: boolean;
  message: string;
  data?: T;
  error?: unknown;
}

/**
 * Common response utility class for standardized API responses
 */
export class ResponseUtil {
  /**
   * Send a successful response
   * @param res - Express response object
   * @param message - Success message
   * @param data - Response data (optional)
   * @param statusCode - HTTP status code (default: 200)
   */
  static success<T>(res: IResponse, message: string, data?: T, statusCode: number = 200): void {
    const response: ApiResponse<T> = {
      success: true,
      message,
      ...(data !== undefined && { data }),
    };

    res.status(statusCode).json(response);
  }

  /**
   * Send an error response
   * @param res - Express response object
   * @param message - Error message
   * @param error - Error details (optional)
   * @param statusCode - HTTP status code (default: 500)
   */
  static error(res: IResponse, message: string, error?: unknown, statusCode: number = 500): void {
    const response: ApiResponse = {
      success: false,
      message,
      ...(error !== undefined && { error }),
    };

    res.status(statusCode).json(response);
  }

  /**
   * Send a created response (201)
   * @param res - Express response object
   * @param message - Success message
   * @param data - Created resource data
   */
  static created<T>(res: IResponse, message: string, data: T): void {
    ResponseUtil.success(res, message, data, 201);
  }

  /**
   * Send a not found response (404)
   * @param res - Express response object
   * @param message - Not found message
   */
  static notFound(res: IResponse, message: string = 'Resource not found'): void {
    ResponseUtil.error(res, message, undefined, 404);
  }

  /**
   * Send a bad request response (400)
   * @param res - Express response object
   * @param message - Bad request message
   * @param error - Error details (optional)
   */
  static badRequest(res: IResponse, message: string, error?: unknown): void {
    ResponseUtil.error(res, message, error, 400);
  }

  /**
   * Send an unauthorized response (401)
   * @param res - Express response object
   * @param message - Unauthorized message
   */
  static unauthorized(res: IResponse, message: string = 'Unauthorized'): void {
    ResponseUtil.error(res, message, undefined, 401);
  }

  /**
   * Send a forbidden response (403)
   * @param res - Express response object
   * @param message - Forbidden message
   */
  static forbidden(res: IResponse, message: string = 'Forbidden'): void {
    ResponseUtil.error(res, message, undefined, 403);
  }

  /**
   * Send a conflict response (409)
   * @param res - Express response object
   * @param message - Conflict message
   * @param error - Error details (optional)
   */
  static conflict(res: IResponse, message: string, error?: unknown): void {
    ResponseUtil.error(res, message, error, 409);
  }

  /**
   * Send an internal server error response (500)
   * @param res - Express response object
   * @param message - Error message
   * @param error - Error details (optional)
   */
  static internalServerError(res: IResponse, message: string = 'Internal Server Error', error?: unknown): void {
    ResponseUtil.error(res, message, error, 500);
  }

  /**
   * Send Validation error response (422)
   * @param res - Express response object
   * @param message - Error message
   * @param error - Error details (optional)
   */

  static validationError(res: IResponse, message: string, error?: unknown): void {
    ResponseUtil.error(res, message, error, 422);
  }
}

// Export individual methods for convenience
export const { success, error, created, notFound, badRequest, unauthorized, forbidden, conflict, internalServerError } =
  ResponseUtil;
