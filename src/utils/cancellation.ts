import { IPayment, IProperty, IReservation, IResponse, ReservationStatusEnum } from '../types';
import ReservationModel from '../models/reservation.model';
import propertyModel from '../models/property.model';
import PaymentModel from '../models/payment.model';
import loggerService from './logger/logger.service';
import PropertyPoliciesModel from '../models/property-policies.model';
import { ResponseUtil } from './response';

interface RefundValidationResult {
  reservation: IReservation;
  property: IProperty;
  cancellationPolicies: RefundPolicy[];
  hoursDifference: number;
  refundPolicy: RefundPolicy;
  refundableAmount: number;
}

type RefundPolicy = {
  hours: number;
  refund_percent: number;
  description: string;
};

export function findRefundPolicy(
  remaining_hours: number,
  policies: RefundPolicy[],
): {
  hours: number;
  refund_percent: number;
  description: string;
} {
  let eligible_policy = null;
  let max_refund = -1;

  for (const policy of policies) {
    if (remaining_hours >= policy.hours && policy.refund_percent > max_refund) {
      eligible_policy = policy;
      max_refund = policy.refund_percent;
    }
  }

  if (eligible_policy) {
    return {
      hours: eligible_policy.hours,
      refund_percent: eligible_policy.refund_percent,
      description: eligible_policy.description,
    };
  } else {
    return {
      hours: 0,
      refund_percent: 0,
      description: 'No refund',
    };
  }
}

export async function validateRefundInputs(
  reservationId: string,
  cancellationTime: string | undefined,
  res: IResponse,
): Promise<RefundValidationResult | null> {
  if (!cancellationTime) {
    loggerService.warn(`Cancellation time not provided`);
    ResponseUtil.badRequest(res, 'Cancellation time not provided');
    return null;
  }

  const reservation = await ReservationModel.findById(reservationId).populate('items');

  if (!reservation) {
    loggerService.warn(`Reservation with id ${reservationId} not found`);
    ResponseUtil.notFound(res, 'Reservation not found');
    return null;
  }

  if (reservation.status === ReservationStatusEnum.CANCELLED) {
    loggerService.warn(`Reservation with id ${reservationId} already cancelled`);
    ResponseUtil.badRequest(res, 'Reservation already cancelled');
    return null;
  }

  const firstItem = reservation.items[0];

  if (new Date(firstItem.startDateTime).getTime() < Date.now()) {
    ResponseUtil.badRequest(res, 'Cannot update past reservation');
    return null;
  }

  const cancellationDate = new Date(cancellationTime);
  const checkInDate = firstItem.startDateTime;
  const timeDifference = checkInDate.getTime() - cancellationDate.getTime();
  const hoursDifference = timeDifference / (1000 * 60 * 60);

  const property = await propertyModel.findById(firstItem.propertyId);
  if (!property) {
    loggerService.warn(`Property with id ${firstItem.propertyId} not found`);
    ResponseUtil.notFound(res, 'Property not found');
    return null;
  }

  const propertyPolicies = await PropertyPoliciesModel.findOne({ propertyId: property._id });
  if (!propertyPolicies) {
    loggerService.warn(`Property policies not found for property ${property.name}`);
    ResponseUtil.notFound(res, 'Property policies not found');
    return null;
  }
  const cancellationPolicies = propertyPolicies.get('cancellationPolicies') ?? [];

  if (!cancellationPolicies || !Array.isArray(cancellationPolicies) || cancellationPolicies.length === 0) {
    loggerService.warn(`Cancellation policies not found for property ${property.name}`);
    ResponseUtil.notFound(res, 'Cancellation policies not found');
    return null;
  }

  const refundPolicy = findRefundPolicy(hoursDifference, cancellationPolicies);
  const refundableAmount = refundPolicy.refund_percent
    ? (refundPolicy.refund_percent / 100) * reservation.grandTotal
    : 0;

  return {
    reservation,
    property,
    cancellationPolicies,
    hoursDifference,
    refundPolicy,
    refundableAmount,
  };
}

export async function validatePayment(reservationId: string, res: IResponse): Promise<IPayment | null> {
  const payment = await PaymentModel.findOne({
    reservationId: reservationId,
    status: 'paid',
  });

  if (!payment) {
    loggerService.warn(`Payment with Reservation id ${reservationId} not found`);
    ResponseUtil.notFound(res, 'Payment not found');
    return null;
  }

  if (payment.status === 'refunded') {
    loggerService.warn(`Payment with Reservation id ${reservationId} already refunded`);
    ResponseUtil.badRequest(res, 'Payment already refunded');
    return null;
  }

  return payment;
}
