import UserSchema from '../models/user.model';
import { IReservation, UserRoleEnum } from '../types';
import { JwtPayloadRequest } from '../services/jwt.service';

export const checkReservationAuthorization = async (user: JwtPayloadRequest, reservation: IReservation) => {
  const userInDB = await UserSchema.findById(user.userId);
  if (!userInDB) {
    return { isAuthorized: false, message: 'User not found' };
  }
  const doesPhoneMatch =
    userInDB.phone.countryCode + userInDB.phone.phoneNumber ===
    reservation.bookerDetails.phone.countryCode + reservation.bookerDetails.phone.phoneNumber;

  if (user.role === UserRoleEnum.USER && reservation.bookerDetails.email !== userInDB.email && !doesPhoneMatch) {
    return { isAuthorized: false, message: "Nice try, but this reservation isn't yours." };
  } else if (user.role === UserRoleEnum.MERCHANT && !user.properties?.includes(reservation.propertyId.toString())) {
    return { isAuthorized: false, message: "Nice try, but this reservation isn't in your property." };
  }

  return { isAuthorized: true, message: null };
};
