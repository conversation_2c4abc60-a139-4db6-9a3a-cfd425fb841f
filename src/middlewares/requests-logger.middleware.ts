import { INextFunction, IRequest, IResponse } from '../types';
import loggerService from '../utils/logger/logger.service';

export const requestLogger = (req: IRequest, res: IResponse, next: INextFunction) => {
  const start = Date.now();

  loggerService.http(`${req.method} ${req.originalUrl}`, 'HTTP', {
    ip: req.ip,
    userAgent: req.get('user-agent'),
  });

  // Log response details when the response is finished
  res.on('finish', () => {
    const duration = Date.now() - start;
    const logLevel = res.statusCode >= 400 ? 'error' : 'http';

    loggerService.log(logLevel, `${req.method} ${req.originalUrl} ${res.statusCode} - ${duration}ms`, 'HTTP', {
      statusCode: res.statusCode,
      duration,
      ip: req.ip,
    });
  });

  next();
};
