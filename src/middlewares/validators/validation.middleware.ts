import { z, ZodError } from 'zod';
import { NextFunction, Request, Response } from 'express';
import loggerService from '../../utils/logger/logger.service';
import { ResponseUtil } from '../../utils/response';

export interface ValidationSchemas {
  body?: z.ZodSchema;
  params?: z.ZodSchema;
  query?: z.ZodSchema;
}

/**
 * Validation middleware factory that creates middleware for validating request data
 * @param schemas - Object containing Zod schemas for body, params, and/or query
 * @returns Express middleware function
 */
export const validate = (schemas: ValidationSchemas) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      // Validate request body
      if (schemas.body) {
        const bodyResult = schemas.body.safeParse(req.body);
        if (!bodyResult.success) {
          const errorMessage =
            bodyResult?.error instanceof ZodError
              ? bodyResult.error.issues
                  .map((issue) => `${issue.path.join('.') || 'unknown'}: ${issue.message}`)
                  .join(', ')
              : 'Invalid request body';
          loggerService.warn(`Body validation failed: ${errorMessage}`, 'validation.middleware.ts', {
            path: req.path,
            method: req.method,
            errors: bodyResult.error.issues,
          });
          ResponseUtil.badRequest(res, errorMessage);
          return;
        }
        req.body = bodyResult.data;
      }

      // Validate request params
      if (schemas.params) {
        const paramsResult = schemas.params.safeParse(req.params);
        if (!paramsResult.success) {
          const errorMessage =
            paramsResult?.error instanceof ZodError
              ? paramsResult.error.issues
                  .map((issue) => `${issue.path.join('.') || 'unknown'}: ${issue.message}`)
                  .join(', ')
              : 'Invalid request parameters';
          loggerService.warn(`Params validation failed: ${errorMessage}`, 'validation.middleware.ts', {
            path: req.path,
            method: req.method,
            errors: paramsResult.error.issues,
          });
          ResponseUtil.badRequest(res, errorMessage);
          return;
        }
        Object.assign(req.params, paramsResult.data);
      }

      // Validate request query
      if (schemas.query) {
        const queryResult = schemas.query.safeParse(req.query);
        if (!queryResult.success) {
          const errorMessage =
            queryResult?.error instanceof ZodError
              ? queryResult.error.issues
                  .map((issue) => `${issue.path.join('.') || 'unknown'}: ${issue.message}`)
                  .join(', ')
              : 'Invalid query parameters';
          loggerService.warn(`Query validation failed: ${errorMessage}`, 'validation.middleware.ts', {
            path: req.path,
            method: req.method,
            errors: queryResult.error.issues,
          });
          ResponseUtil.badRequest(res, errorMessage);
          return;
        }
        Object.assign(req.query, queryResult.data);
      }

      next();
    } catch (error) {
      loggerService.error('Validation middleware error:', 'validation.middleware.ts', error);
      ResponseUtil.validationError(res, 'Validation failed', error);
    }
  };
};

/**
 * Convenience function for validating only request body
 * @param schema - Zod schema for request body
 * @returns Express middleware function
 */
export const validateBody = (schema: z.ZodSchema) => {
  return validate({ body: schema });
};

/**
 * Convenience function for validating only request params
 * @param schema - Zod schema for request params
 * @returns Express middleware function
 */
export const validateParams = (schema: z.ZodSchema) => {
  return validate({ params: schema });
};

/**
 * Convenience function for validating only query parameters
 * @param schema - Zod schema for query parameters
 * @returns Express middleware function
 */
export const validateQuery = (schema: z.ZodSchema) => {
  return validate({ query: schema });
};
