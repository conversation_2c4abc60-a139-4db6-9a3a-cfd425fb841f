import { INextFunction, IRequest, IResponse } from '../types';
import loggerService from '../utils/logger/logger.service';
import { ResponseUtil } from '../utils/response';
import { NODE_ENV } from '../constants';

interface IError extends Error {
  code?: number | string;
  statusCode?: number;
  errors?: Record<string, { message: string }>;
}

export const errorHandler = (err: IError, _req: IRequest, res: IResponse, _next: INextFunction) => {
  if (NODE_ENV !== 'production') {
    console.error(err);
  }
  let statusCode = 500;
  let message = 'Internal Server Error';
  let errorDetail: string | string[] | Record<string, string[]> | undefined = err.message;

  // Handle specific error types
  if (err.name === 'ValidationError' && err.errors) {
    statusCode = 400;
    message = 'Validation failed';
    errorDetail = Object.keys(err.errors).reduce(
      (acc, key) => {
        if (err.errors) {
          acc[key] = [err.errors[key].message];
        }
        return acc;
      },
      {} as Record<string, string[]>,
    );
  } else if (err.name === 'CastError') {
    statusCode = 400;
    message = 'Invalid ID format';
    errorDetail = err.message;
  } else if (err.code === 11000) {
    statusCode = 409;
    message = 'Duplicate key error';
    errorDetail = err.message;
  } else if (err.message && err.message.includes && err.message.includes('CORS')) {
    statusCode = 403;
    message = 'CORS Error';
    errorDetail = 'Are you at the right place?';
  } else if (err.statusCode) {
    statusCode = err.statusCode;
    message = err.message;
    errorDetail = err.message;
  }

  const logMsg = `[${statusCode}] ${message}: ${typeof errorDetail === 'string' ? errorDetail : err.message}`;
  if (statusCode >= 500) {
    loggerService.error(logMsg);
  } else {
    loggerService.warn(logMsg);
  }

  ResponseUtil.error(res, message, errorDetail, statusCode);
};
