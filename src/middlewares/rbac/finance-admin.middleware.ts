import { ResponseUtil } from '../../utils/response';
import { INextFunction, IRequest, IResponse, UserRoleEnum } from '../../types';

export const financeAdminMiddleware = (req: IRequest, res: IResponse, next: INextFunction): void => {
  const role = req.user?.role;
  if (!role) {
    return ResponseUtil.unauthorized(res, 'Unauthorized');
  }
  if (role !== UserRoleEnum.FINANCE_ADMIN) {
    return ResponseUtil.forbidden(res, 'Forbidden');
  }
  next();
};
