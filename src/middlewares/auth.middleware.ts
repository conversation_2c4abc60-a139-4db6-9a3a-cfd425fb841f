import loggerService from '../utils/logger/logger.service';
import { JwtService } from '../services/jwt.service';
import { NODE_ENV } from '../constants';
import { IRequest, IResponse, INextFunction } from '../types';
import { ResponseUtil } from '../utils/response';

const jwtService = JwtService.getInstance();

/**
 * Authentication middleware for protected routes
 * Authenticates user using access token or refresh token
 * If access token is expired, refresh token is used to generate a new access token
 * Authenticated user data is attached to request object
 * @param req
 * @param res
 * @param next
 * @returns void
 */

export const authMiddleware = (req: IRequest, res: IResponse, next: INextFunction): void => {
  const accessToken = req.cookies?.accessToken;
  const refreshToken = req.cookies?.refreshToken;

  if (!accessToken && !refreshToken) {
    loggerService.warn('No token provided');
    return ResponseUtil.unauthorized(res, 'Unauthorized');
  }

  if (accessToken) {
    try {
      const decoded = jwtService.verifyAccessToken(accessToken);
      req.user = {
        userId: decoded.userId,
        role: decoded.role,
        properties: decoded.properties,
      };
      return next();
    } catch (error) {
      const errorMessage = (error as Error).message;
      loggerService.warn(`Access token invalid: ${errorMessage}`);

      if (!errorMessage.includes('expired')) {
        return ResponseUtil.unauthorized(res, 'Invalid access token');
      }
    }
  }

  if (!refreshToken) {
    loggerService.warn('No refresh token available');
    return ResponseUtil.unauthorized(res, 'Unauthorized');
  }

  try {
    const decodedRefresh = jwtService.verifyRefreshToken(refreshToken);

    const newAccessToken = jwtService.generateAccessToken({
      userId: decodedRefresh.userId,
      role: decodedRefresh.role,
      properties: decodedRefresh.properties,
    });

    res.cookie('accessToken', newAccessToken, {
      httpOnly: true,
      secure: NODE_ENV !== 'development',
      sameSite: NODE_ENV === 'production' ? 'lax' : 'none',
      maxAge: 15 * 60 * 1000,
      path: '/',
      partitioned: NODE_ENV !== 'production',
    });

    loggerService.info(`Access token refreshed for user: ${decodedRefresh.userId}`);

    req.user = {
      userId: decodedRefresh.userId,
      role: decodedRefresh.role,
      properties: decodedRefresh.properties,
    };

    return next();
  } catch (refreshError) {
    loggerService.error(`Failed to verify refresh token: ${(refreshError as Error).message}`);
    return ResponseUtil.unauthorized(res, 'Invalid refresh token');
  }
};
