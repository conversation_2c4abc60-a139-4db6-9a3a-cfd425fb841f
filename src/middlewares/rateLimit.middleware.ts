import { Request, Response, NextFunction } from 'express';
import rateLimit from 'express-rate-limit';
import { RATE_LIMIT_MAX, RATE_LIMIT_WINDOW_MS } from '../constants';

/**
 * Rate limiting middleware to prevent abuse and DDoS attacks
 * @param req
 * @param res
 * @param next
 * @returns void
 */

const limiter = rateLimit({
  windowMs: RATE_LIMIT_WINDOW_MS,
  limit: RATE_LIMIT_MAX,
  message: {
    error: "Your IP's so popular, give it a rest and try again later.",
    status: 429,
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const rateLimitMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  limiter(req, res, next);
};

export default rateLimitMiddleware;
