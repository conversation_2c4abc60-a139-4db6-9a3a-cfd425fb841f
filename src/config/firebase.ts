import admin from 'firebase-admin';
import { FIREBASE_CLIENT_EMAIL, FIREBASE_PRIVATE_KEY, FIREBASE_PROJECT_ID } from '../constants';
import loggerService from '../utils/logger/logger.service';

const serviceAccount = {
  project_id: FIREBASE_PROJECT_ID,
  private_key: FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
  client_email: FIREBASE_CLIENT_EMAIL,
};

try {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount as admin.ServiceAccount),
  });
  loggerService.info('Firebase Admin initialized successfully');
} catch (error) {
  loggerService.error('Error initializing Firebase Admin:', 'firebase.ts', error as Record<string, unknown>);
}

export { admin };
