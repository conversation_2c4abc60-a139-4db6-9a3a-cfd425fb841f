import mongoose from 'mongoose';
import loggerService from '../utils/logger/logger.service';
import { MONGO_URI } from '../constants';

const connectDB = async (): Promise<void> => {
  try {
    const conn = await mongoose.connect(MONGO_URI, {});
    loggerService.info(`MongoDB connected to ${conn.connection.name} successfully`);
  } catch (error) {
    loggerService.error(`MongoDB connection error: ${(error as Error).message}`);
    process.exit(1);
  }
};

export default connectDB;
