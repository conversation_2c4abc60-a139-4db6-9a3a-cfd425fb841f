import express, { Application } from 'express';
import helmet from 'helmet';
import routes from './routes';
import cookieParser from 'cookie-parser';
import { corsMiddleware } from './middlewares/cors.middleware';
import { IRequest, IResponse } from './types';
import { errorHand<PERSON> } from './middlewares/error.middleware';
import { requestLogger } from './middlewares/requests-logger.middleware';
import { NODE_ENV } from './constants';
import rateLimitMiddleware from './middlewares/rateLimit.middleware';

const app: Application = express();

app.use(express.json());
app.use(corsMiddleware);

if (NODE_ENV !== 'QA') {
  app.set('trust proxy', 1);
  app.use(
    helmet({
      contentSecurityPolicy: false, // Not serving HTML, disable
      crossOriginEmbedderPolicy: false, // Not needed for APIs
      crossOriginOpenerPolicy: false, // Not needed for APIs
      crossOriginResourcePolicy: false, // Not serving static files, disable
      referrerPolicy: { policy: 'no-referrer' }, // For privacy
      hsts: {
        maxAge: 60 * 60 * 24 * 365, // 1 year
        includeSubDomains: true, // Include sub domains
        preload: true, // Preload in browsers
      },
      frameguard: false, // Not serving HTML, disable
      xssFilter: false, // Not needed for APIs
      noSniff: true, // For security no sniffing
      dnsPrefetchControl: { allow: false }, // For privacy
    }),
  );
  app.use(rateLimitMiddleware);
}

app.use(cookieParser());

app.use(requestLogger);

app.get('/', (_req: IRequest, res: IResponse) => {
  res.json({
    message: 'Welcome to Stay Transit Backend',
    version: '1.0.0',
  });
});
app.use('/api', routes);

app.use(errorHandler);

export default app;
