export enum PaymentStatusEnum {
  PENDING = 'pending',
  UNPAID = 'unpaid',
  PAID = 'paid',
  FAILED = 'failed',
  REFUNDED = 'refunded',
}

export enum PaymentMethod {
  CARD = 'card',
  BANK_TRANSFER = 'bank_transfer',
  CASH = 'cash',
  CHEQUE = 'cheque',
}

export enum PaymentType {
  ONLINE = 'online',
  OFFLINE = 'offline',
}

export enum PaymentModeEnum {
  CASH = 'cash',
  CHEQUE = 'cheque',
  ONLINE = 'online',
  BANK_TRANSFER = 'bank_transfer',
}
