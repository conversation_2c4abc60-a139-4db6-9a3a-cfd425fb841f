export interface LineItem {
  price_data: {
    currency: string;
    product_data: {
      name: string;
      description?: string;
      images?: string[];
      metadata?: { [key: string]: string };
    };
    unit_amount: number;
    unit_amount_decimal?: string;
  };
  quantity: number;
  tax_rates?: string[];
  adjustable_quantity?: {
    enabled: boolean;
    minimum?: number;
    maximum?: number;
  };
  dynamic_tax_rates?: string[];
  metadata?: { [key: string]: string };
}
