import mongoose from 'mongoose';
import { IBase } from './base.types';

export interface IPropertyDetails extends IBase {
  propertyId: mongoose.Types.ObjectId;
  isPropertyChain: boolean;
  totalUnits: number;
  noOfReservationsPerPerson: number;
  minReservationLength: number;
  maxReservationLength: number;
  bookingIdPrefix: string;
  invoiceIdPrefix: string;
  frontDeskOpeningTime: string;
  frontDeskClosingTime: string;
  isOpen24Hours: boolean;
  aboutProperty: string;
  businessLogo: string;
  isAllUnitsInSameAddress: boolean;
  currency: mongoose.Types.ObjectId;
  amenities: mongoose.Types.ObjectId[];
  mailEvents: mongoose.Types.ObjectId[];
  accommodationType?: mongoose.Types.ObjectId;
  attachments: string[];

  sharedUnits?: number;
  privateUnits?: number;
  isForExclusiveUse?: boolean;
  areaSize?: number;
}
