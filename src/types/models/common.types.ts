import { Document, Types } from 'mongoose';

export interface IPhone extends Document {
  countryCode: string;
  phoneNumber: string;
}

export interface IContact extends Document {
  firstName: string;
  lastName: string;
  email: string;
  phone: IPhone;
}

export interface IAddress extends Document {
  address1: string;
  address2: string;
  city: string;
  state: string;
  country: string;
  zipcode: string;
  latitude: number;
  longitude: number;
  locationId?: Types.ObjectId;
  placeId?: string;
}

export interface ITax extends Document {
  pan: string;
  tan: string;
  gstn: string;
  iata: string;
}
