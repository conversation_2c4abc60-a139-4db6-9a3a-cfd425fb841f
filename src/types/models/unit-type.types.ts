import mongoose from 'mongoose';
import { IBase } from './base.types';

export interface IUnitType extends IBase {
  // Required Fields
  code: string;
  name: string;
  description: string;
  totalUnits: number;
  attachments: string[];
  propertyId: mongoose.Types.ObjectId;
  bufferTime: number;
  serviceTypeId: mongoose.Types.ObjectId;

  // Optional Fields
  // Stays, Capsules
  bedType: string;
  // Stays, Shower
  bathroomType: string;
  // Stays, Workspace
  capacity: number;
  // Stays, Workspace, Shower
  area: number;
  // Stays
  isSmokingAllowed: boolean;
}
