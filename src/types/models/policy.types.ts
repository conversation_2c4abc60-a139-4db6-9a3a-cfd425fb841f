import { IBase } from './base.types';
import mongoose from 'mongoose';

export interface IPolicy extends IBase {
  name: string;
  serviceType?: string;
  order: number;
  description?: string;
  category?: mongoose.Types.ObjectId;
  input_type: 'text' | 'radio' | 'checkbox' | 'select' | 'custom_rule';
  options?: Array<{ value: string; description: string }>;
  is_required: boolean;
  validation_rules?: {
    min?: number;
    max?: number;
    pattern?: string;
    required?: boolean;
  };
  conditional_logic?: {
    field: string;
    value: unknown;
    operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than';
  };
  config?: {
    rules?: Array<{
      refund_percent?: number;
      before_hours?: number;
      after_hours?: number;
      [key: string]: unknown;
    }>;
    [key: string]: unknown;
  };
  propertyId?: string;
}
