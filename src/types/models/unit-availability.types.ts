import { Schema } from 'mongoose';
import { IBase } from './base.types';

export interface IUnitAvailability extends IBase {
  // Legacy fields (keeping for backward compatibility)
  propertyId: Schema.Types.ObjectId;
  unitTypeId: Schema.Types.ObjectId;
  dateTime?: Date; // Optional for rule-based system
  availability?: number; // Optional for rule-based system (renamed from quantityAvailable)

  // New rule-based fields
  fromDate?: Date; // Start date of the rule
  toDate?: Date; // End date of the rule
  days?: string[]; // Array of days (e.g., ["monday", "tuesday", "all"])
  fromTime?: string; // Start time (default "00:00")
  toTime?: string; // End time (default "23:59")
  quantityAvailable?: number; // Units available (0 = blocked) - for rule-based system
  reason?: string; // Optional text reason for blocking
  isActive?: boolean; // Whether the rule is currently active
  version?: number; // Version number for rule management
  archivedAt?: Date; // When the rule was archived (if applicable)
  archivedReason?: string; // Reason for archiving
}
