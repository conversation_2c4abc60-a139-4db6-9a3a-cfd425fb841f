import { Schema } from 'mongoose';
import { IBase } from './base.types';

// Legacy unit availability interface (keeping for backward compatibility)
export interface IUnitAvailability extends IBase {
  propertyId: Schema.Types.ObjectId;
  unitTypeId: Schema.Types.ObjectId;
  dateTime: Date;
  availability: number;
}

// New availability rule interface for rule-based availability management
export interface IAvailabilityRule extends IBase {
  unitTypeId: Schema.Types.ObjectId; // ID of the unit type
  fromDate: Date; // Start date of the rule
  toDate: Date; // End date of the rule
  days: string[]; // Array of days (e.g., ["monday", "tuesday", "all"])
  fromTime: string; // Start time (default "00:00")
  toTime: string; // End time (default "23:59")
  quantityAvailable: number; // Units available (0 = blocked)
  reason?: string; // Optional text reason for blocking
  isActive: boolean; // Whether the rule is currently active
  version: number; // Version number for rule management
  archivedAt?: Date; // When the rule was archived (if applicable)
  archivedReason?: string; // Reason for archiving
}
