import mongoose, { Types } from 'mongoose';
import { IBase } from './base.types';
import { IAddress } from './common.types';
import { PremisesTypeEnum, PropertyStatusEnum } from '../enums';

// TODO: Rename to business
export interface IProperty extends IBase {
  serviceType: mongoose.Types.ObjectId;
  name: string;
  description: string;
  premisesType: PremisesTypeEnum;
  code: string;
  website: string;
  address: IAddress;
  owner: Types.ObjectId;
  status: PropertyStatusEnum;
  buildYear: Date;
  acceptBookingFrom: Date;
}
