import { Schema } from 'mongoose';
import { IBase } from './base.types';
import { PaymentStatusEnum, PaymentMethod, PaymentType } from '../enums/payment.enums';

export interface IPayment extends IBase {
  reservationId: Schema.Types.ObjectId;
  propertyId: Schema.Types.ObjectId;
  code: string;
  amount: number;
  currency: string;
  status: PaymentStatusEnum;
  paymentMethod: PaymentMethod;
  paymentType: PaymentType;
  paymentGateway: string;
  paymentGatewayResponse: Record<string, unknown>;
  refundResponse?: Record<string, unknown>;
}
