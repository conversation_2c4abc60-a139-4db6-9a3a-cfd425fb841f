import mongoose from 'mongoose';
import { IBase } from './base.types';

export interface IPropertyPolicy {
  policyId: mongoose.Types.ObjectId;
  value: string;
}

export interface IPropertyCancellationPolicy {
  hours: number;
  refund_percent: number;
  description: string;
}

export interface IPropertyCustomPolicy {
  title: string;
  description: string;
}

export interface IPropertyPolicies extends IBase {
  propertyId: mongoose.Types.ObjectId;
  policies: IPropertyPolicy[];
  cancellationPolicies: IPropertyCancellationPolicy[];
  customPolicies: IPropertyCustomPolicy[];
}
