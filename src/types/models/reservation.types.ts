import { Types, Document } from 'mongoose';
import { IPhone, IAddress } from './common.types';
import { ReservationStatusEnum, GenderEnum } from '../enums/reservation.enums';
import { PaymentStatusEnum } from '../enums/payment.enums';
import { IDomainValue } from './domain-value.types';

export interface IGuestDetails {
  _id: Types.ObjectId;
  firstName: string;
  lastName: string;
  email: string;
  phone: IPhone;
  address: IAddress;
  gender: GenderEnum;
}

export interface IBookerDetails {
  firstName: string;
  lastName: string;
  email: string;
  phone: IPhone;
  address: IAddress;
  gender: GenderEnum;
}

export interface IFlightDetails {
  number?: string;
  from?: string;
  to?: string;
  arrivalDateTime?: Date;
  departureDateTime?: Date;
}

export interface IReservationItem extends Document {
  reservationCode: string;
  propertyId: Types.ObjectId;
  couponDiscount?: number;
  unitTypeId: Types.ObjectId;
  packageId: Types.ObjectId;
  startDateTime: Date;
  endDateTime: Date;
  noOfAdults: number;
  noOfChildren: number;
  price: number;
  taxes: IDomainValue[];
  tax: number;
  totalAmount: number;
  guestDetails: IGuestDetails[];
  specialRequest: string;
  flightDetails: IFlightDetails;
  bookerDetails: IBookerDetails;
  status: ReservationStatusEnum;
  paymentStatus: PaymentStatusEnum;
  refundAmount?: number;
  cancellationReason?: string;
}

export interface IReservation extends Document {
  reservationCode: string;
  invoiceCode: string;
  propertyId: Types.ObjectId;
  items: IReservationItem[];
  subTotal: number;
  totalTax: number;
  grandTotal: number;
  status: ReservationStatusEnum;
  paymentStatus: PaymentStatusEnum;
  bookerDetails: IBookerDetails;
  refundAmount?: number;
  cancellationReason?: string;
}
