import { Types } from 'mongoose';
import { IBase } from './base.types';

export interface IRateCard extends IBase {
  // Legacy fields (keeping for backward compatibility)
  name?: string;
  description?: string;
  propertyId: Types.ObjectId;
  dateTime?: Date; // Optional for backward compatibility
  packageId?: Types.ObjectId; // Optional for new rule-based system
  price?: number; // Optional for new rule-based system

  // New rule-based fields
  unitId?: Types.ObjectId; // Links to unit model (for rule-based system)
  fromDate?: Date; // Start date of the rule
  toDate?: Date; // End date of the rule
  days?: string[]; // Array of days (e.g., ["monday", "tuesday", "all"])
  fromTime?: string; // Start time (default "00:00")
  toTime?: string; // End time (default "23:59")
  percentageChange?: number; // Signed percentage (+/-)
  isActive?: boolean; // Whether the rule is currently active
  version?: number; // Version number for rule management
  archivedAt?: Date; // When the rule was archived (if applicable)
  archivedReason?: string; // Reason for archiving
}
