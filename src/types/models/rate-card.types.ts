import { Types } from 'mongoose';
import { IBase } from './base.types';

// Legacy rate card interface (keeping for backward compatibility)
export interface IRateCard extends IBase {
  name: string;
  description: string;
  propertyId: Types.ObjectId;
  dateTime: Date;
  packageId: Types.ObjectId;
  price: number;
}

// New rate card rule interface for rule-based pricing
export interface IRateCardRule extends IBase {
  unitId: Types.ObjectId; // Links to unit model
  fromDate: Date; // Start date of the rule
  toDate: Date; // End date of the rule
  days: string[]; // Array of days (e.g., ["monday", "tuesday", "all"])
  fromTime: string; // Start time (default "00:00")
  toTime: string; // End time (default "23:59")
  percentageChange: number; // Signed percentage (+/-)
  isActive: boolean; // Whether the rule is currently active
  version: number; // Version number for rule management
  archivedAt?: Date; // When the rule was archived (if applicable)
  archivedReason?: string; // Reason for archiving
}
