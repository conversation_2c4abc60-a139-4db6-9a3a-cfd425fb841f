import { PutObjectCommand, PutObjectCommandInput, S3Client } from '@aws-sdk/client-s3';
import { v4 as uuidv4 } from 'uuid';
import { AWS_ACCESS_KEY_ID, AWS_REGION, AWS_S3_BUCKET_NAME, AWS_SECRET_ACCESS_KEY } from '../constants';

export const uploadImage = async (file: Express.Multer.File): Promise<string> => {
  const fileName = `${uuidv4()}-${file.originalname}`;
  const params: PutObjectCommandInput = {
    Bucket: AWS_S3_BUCKET_NAME,
    Key: fileName,
    Body: file.buffer,
    ContentType: file.mimetype,
    ACL: 'public-read',
  };
  const s3 = new S3Client({
    region: AWS_REGION,
    credentials: {
      accessKeyId: AWS_ACCESS_KEY_ID,
      secretAccessKey: AWS_SECRET_ACCESS_KEY,
    },
  });
  await s3.send(new PutObjectCommand(params));
  return fileName;
};

// export const deleteImage = async (key: string): Promise<void> => {
//     const params = { Bucket: process.env.AWS_S3_BUCKET, Key: key };
//     await s3.send(new DeleteObjectCommand(params));
// };

export const getImageUrl = async (key: string): Promise<string> => {
  return `https://${AWS_S3_BUCKET_NAME}.s3.${AWS_REGION}.amazonaws.com/${key}`;
};

// export const downloadImage = async (key: string) => {
//     const command = new GetObjectCommand({ Bucket: process.env.AWS_S3_BUCKET, Key: key });
//     return await s3.send(command);
// };
