import mongoose from 'mongoose';
import RateCardRuleModel from '../models/rate-card-rule.model';
import UnitTypeModel from '../models/unit-type.model';
import PackageModel from '../models/package.model';
import { IRateCardRule } from '../types';

export interface RateCardRuleInput {
  unitId: string;
  fromDate: Date;
  toDate: Date;
  days: string[];
  fromTime?: string;
  toTime?: string;
  percentageChange: number;
}

export interface RateResolutionResult {
  totalRate: number;
  hourlyBreakdown: Array<{
    hour: Date;
    baseRate: number;
    appliedRule?: IRateCardRule;
    adjustedRate: number;
  }>;
}

export interface OverlapResolution {
  action: 'override' | 'truncate';
  affectedRules: IRateCardRule[];
  newRule: IRateCardRule;
}

export class RateCardRuleService {
  private static instance: RateCardRuleService;

  public static getInstance(): RateCardRuleService {
    if (!RateCardRuleService.instance) {
      RateCardRuleService.instance = new RateCardRuleService();
    }
    return RateCardRuleService.instance;
  }

  private constructor() {}

  /**
   * Create a new rate card rule with overlap detection and resolution
   */
  async createRule(ruleInput: RateCardRuleInput): Promise<OverlapResolution> {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Validate unit exists
      const unitType = await UnitTypeModel.findById(ruleInput.unitId).session(session);
      if (!unitType) {
        throw new Error('Unit type not found');
      }

      // Find overlapping rules
      const overlappingRules = await this.findOverlappingRules(ruleInput, session);

      // Create the new rule
      const newRule = new RateCardRuleModel({
        unitId: ruleInput.unitId,
        fromDate: ruleInput.fromDate,
        toDate: ruleInput.toDate,
        days: ruleInput.days.map(day => day.toLowerCase()),
        fromTime: ruleInput.fromTime || '00:00',
        toTime: ruleInput.toTime || '23:59',
        percentageChange: ruleInput.percentageChange,
        isActive: true,
        version: 1
      });

      let resolution: OverlapResolution;

      if (overlappingRules.length === 0) {
        // No overlaps, simply save the new rule
        await newRule.save({ session });
        resolution = {
          action: 'override',
          affectedRules: [],
          newRule
        };
      } else {
        // Handle overlaps
        resolution = await this.resolveOverlaps(overlappingRules, newRule, session);
      }

      await session.commitTransaction();
      return resolution;
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Find overlapping rules for a given rule input
   */
  private async findOverlappingRules(
    ruleInput: RateCardRuleInput,
    session?: mongoose.ClientSession
  ): Promise<IRateCardRule[]> {
    const query = {
      unitId: ruleInput.unitId,
      isActive: true,
      $or: [
        // Date range overlaps
        {
          fromDate: { $lte: ruleInput.toDate },
          toDate: { $gte: ruleInput.fromDate }
        }
      ],
      // Days overlap (check if any day in the new rule matches any day in existing rules)
      $and: [
        {
          $or: [
            { days: { $in: ruleInput.days.map(day => day.toLowerCase()) } },
            { days: 'all' },
            { $expr: { $in: ['all', ruleInput.days.map(day => day.toLowerCase())] } }
          ]
        }
      ]
    };

    const options = session ? { session } : {};
    return await RateCardRuleModel.find(query, null, options);
  }

  /**
   * Resolve overlaps between existing rules and a new rule
   */
  private async resolveOverlaps(
    overlappingRules: IRateCardRule[],
    newRule: IRateCardRule,
    session: mongoose.ClientSession
  ): Promise<OverlapResolution> {
    const affectedRules: IRateCardRule[] = [];

    for (const existingRule of overlappingRules) {
      const isExactMatch = this.isExactMatch(existingRule, newRule);

      if (isExactMatch) {
        // Exact match: Archive old rule and create new version
        existingRule.isActive = false;
        existingRule.archivedAt = new Date();
        existingRule.archivedReason = 'Overridden by new rule';
        await existingRule.save({ session });
        
        newRule.version = existingRule.version + 1;
        affectedRules.push(existingRule);
      } else {
        // Partial overlap: Truncate old rule
        const truncatedRule = await this.truncateRule(existingRule, newRule, session);
        if (truncatedRule) {
          affectedRules.push(truncatedRule);
        }
      }
    }

    await newRule.save({ session });

    return {
      action: affectedRules.length > 0 ? 'truncate' : 'override',
      affectedRules,
      newRule
    };
  }

  /**
   * Check if two rules are an exact match
   */
  private isExactMatch(rule1: IRateCardRule, rule2: IRateCardRule): boolean {
    return (
      rule1.fromDate.getTime() === rule2.fromDate.getTime() &&
      rule1.toDate.getTime() === rule2.toDate.getTime() &&
      rule1.fromTime === rule2.fromTime &&
      rule1.toTime === rule2.toTime &&
      JSON.stringify(rule1.days.sort()) === JSON.stringify(rule2.days.sort())
    );
  }

  /**
   * Truncate an existing rule to avoid overlap with a new rule
   */
  private async truncateRule(
    existingRule: IRateCardRule,
    newRule: IRateCardRule,
    session: mongoose.ClientSession
  ): Promise<IRateCardRule | null> {
    // For simplicity, we'll archive the old rule and let the new rule take precedence
    // In a more complex implementation, you could split the rule into non-overlapping parts
    existingRule.isActive = false;
    existingRule.archivedAt = new Date();
    existingRule.archivedReason = 'Truncated due to overlap with new rule';
    await existingRule.save({ session });
    
    return existingRule;
  }

  /**
   * Resolve rate for a booking query
   */
  async resolveRate(
    unitId: string,
    packageId: string,
    startDateTime: Date,
    endDateTime: Date
  ): Promise<RateResolutionResult> {
    // Get base rate from package
    const packageDoc = await PackageModel.findById(packageId);
    if (!packageDoc) {
      throw new Error('Package not found');
    }

    const baseHourlyRate = packageDoc.price; // Assuming price is per hour
    const hourlyBreakdown: RateResolutionResult['hourlyBreakdown'] = [];
    let totalRate = 0;

    // Generate hourly slots
    const currentHour = new Date(startDateTime);
    currentHour.setMinutes(0, 0, 0); // Round to hour

    while (currentHour < endDateTime) {
      const nextHour = new Date(currentHour);
      nextHour.setHours(currentHour.getHours() + 1);

      // Find the most recent active rule that matches this hour
      const applicableRule = await this.findApplicableRule(unitId, currentHour);
      
      let adjustedRate = baseHourlyRate;
      if (applicableRule) {
        adjustedRate = baseHourlyRate * (1 + applicableRule.percentageChange / 100);
      }

      hourlyBreakdown.push({
        hour: new Date(currentHour),
        baseRate: baseHourlyRate,
        appliedRule: applicableRule || undefined,
        adjustedRate
      });

      totalRate += adjustedRate;
      currentHour.setHours(currentHour.getHours() + 1);
    }

    return {
      totalRate,
      hourlyBreakdown
    };
  }

  /**
   * Find the most applicable rule for a specific date and time
   */
  private async findApplicableRule(unitId: string, dateTime: Date): Promise<IRateCardRule | null> {
    const dayName = dateTime.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
    const timeString = dateTime.toTimeString().substring(0, 5); // HH:MM format

    const rules = await RateCardRuleModel.find({
      unitId,
      isActive: true,
      fromDate: { $lte: dateTime },
      toDate: { $gte: dateTime },
      $or: [
        { days: 'all' },
        { days: dayName }
      ],
      fromTime: { $lte: timeString },
      toTime: { $gte: timeString }
    }).sort({ createdAt: -1 }); // Most recent first

    return rules.length > 0 ? rules[0] : null;
  }

  /**
   * Get all active rules for a unit
   */
  async getRulesForUnit(unitId: string): Promise<IRateCardRule[]> {
    return await RateCardRuleModel.find({
      unitId,
      isActive: true
    }).sort({ fromDate: 1 });
  }

  /**
   * Archive a rule
   */
  async archiveRule(ruleId: string, reason?: string): Promise<IRateCardRule> {
    const rule = await RateCardRuleModel.findById(ruleId);
    if (!rule) {
      throw new Error('Rule not found');
    }

    rule.isActive = false;
    rule.archivedAt = new Date();
    rule.archivedReason = reason || 'Manually archived';
    
    return await rule.save();
  }
}
