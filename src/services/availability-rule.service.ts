import mongoose from 'mongoose';
import AvailabilityRuleModel from '../models/availability-rule.model';
import UnitTypeModel from '../models/unit-type.model';
import ReservationItemModel from '../models/reservation-item.model';
import { IAvailabilityRule, PaymentStatusEnum, ReservationStatusEnum } from '../types';

export interface AvailabilityRuleInput {
  unitTypeId: string;
  fromDate: Date;
  toDate: Date;
  days: string[];
  fromTime?: string;
  toTime?: string;
  quantityAvailable: number;
  reason?: string;
}

export interface AvailabilityResolutionResult {
  available: number;
  reason?: string;
  hourlyBreakdown: Array<{
    hour: Date;
    totalCapacity: number;
    ruleQuantity?: number;
    confirmedBookings: number;
    availableUnits: number;
    blockReason?: string;
  }>;
}

export interface OverlapResolution {
  action: 'merge' | 'override';
  affectedRules: IAvailabilityRule[];
  newRule: IAvailabilityRule;
}

export class AvailabilityRuleService {
  private static instance: AvailabilityRuleService;

  public static getInstance(): AvailabilityRuleService {
    if (!AvailabilityRuleService.instance) {
      AvailabilityRuleService.instance = new AvailabilityRuleService();
    }
    return AvailabilityRuleService.instance;
  }

  private constructor() {}

  /**
   * Create a new availability rule with overlap detection and resolution
   */
  async createRule(ruleInput: AvailabilityRuleInput): Promise<OverlapResolution> {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Validate unit type exists
      const unitType = await UnitTypeModel.findById(ruleInput.unitTypeId).session(session);
      if (!unitType) {
        throw new Error('Unit type not found');
      }

      // Validate quantity doesn't exceed total units
      if (ruleInput.quantityAvailable > unitType.totalUnits) {
        throw new Error(`Quantity available (${ruleInput.quantityAvailable}) cannot exceed total units (${unitType.totalUnits})`);
      }

      // Find overlapping rules
      const overlappingRules = await this.findOverlappingRules(ruleInput, session);

      // Create the new rule
      const newRule = new AvailabilityRuleModel({
        unitTypeId: ruleInput.unitTypeId,
        fromDate: ruleInput.fromDate,
        toDate: ruleInput.toDate,
        days: ruleInput.days.map(day => day.toLowerCase()),
        fromTime: ruleInput.fromTime || '00:00',
        toTime: ruleInput.toTime || '23:59',
        quantityAvailable: ruleInput.quantityAvailable,
        reason: ruleInput.reason,
        isActive: true,
        version: 1
      });

      let resolution: OverlapResolution;

      if (overlappingRules.length === 0) {
        // No overlaps, simply save the new rule
        await newRule.save({ session });
        resolution = {
          action: 'override',
          affectedRules: [],
          newRule
        };
      } else {
        // Handle overlaps
        resolution = await this.resolveOverlaps(overlappingRules, newRule, session);
      }

      await session.commitTransaction();
      return resolution;
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Create a quick block rule (single date with 0 availability)
   */
  async createQuickBlock(
    unitTypeId: string,
    date: Date,
    fromTime: string = '00:00',
    toTime: string = '23:59',
    reason: string
  ): Promise<OverlapResolution> {
    return this.createRule({
      unitTypeId,
      fromDate: date,
      toDate: date,
      days: ['all'],
      fromTime,
      toTime,
      quantityAvailable: 0,
      reason
    });
  }

  /**
   * Find overlapping rules for a given rule input
   */
  private async findOverlappingRules(
    ruleInput: AvailabilityRuleInput,
    session?: mongoose.ClientSession
  ): Promise<IAvailabilityRule[]> {
    const query = {
      unitTypeId: ruleInput.unitTypeId,
      isActive: true,
      $or: [
        // Date range overlaps
        {
          fromDate: { $lte: ruleInput.toDate },
          toDate: { $gte: ruleInput.fromDate }
        }
      ],
      // Days overlap
      $and: [
        {
          $or: [
            { days: { $in: ruleInput.days.map(day => day.toLowerCase()) } },
            { days: 'all' },
            { $expr: { $in: ['all', ruleInput.days.map(day => day.toLowerCase())] } }
          ]
        }
      ]
    };

    const options = session ? { session } : {};
    return await AvailabilityRuleModel.find(query, null, options);
  }

  /**
   * Resolve overlaps between existing rules and a new rule
   */
  private async resolveOverlaps(
    overlappingRules: IAvailabilityRule[],
    newRule: IAvailabilityRule,
    session: mongoose.ClientSession
  ): Promise<OverlapResolution> {
    const affectedRules: IAvailabilityRule[] = [];

    for (const existingRule of overlappingRules) {
      const isExactMatch = this.isExactMatch(existingRule, newRule);

      if (isExactMatch) {
        // Exact match: Archive old rule and create new version
        existingRule.isActive = false;
        existingRule.archivedAt = new Date();
        existingRule.archivedReason = 'Overridden by new rule';
        await existingRule.save({ session });
        
        newRule.version = existingRule.version + 1;
        affectedRules.push(existingRule);
      } else {
        // Partial overlap: Merge with minimum quantity
        const mergedRule = await this.mergeRules(existingRule, newRule, session);
        if (mergedRule) {
          affectedRules.push(mergedRule);
        }
      }
    }

    await newRule.save({ session });

    return {
      action: affectedRules.length > 0 ? 'merge' : 'override',
      affectedRules,
      newRule
    };
  }

  /**
   * Check if two rules are an exact match
   */
  private isExactMatch(rule1: IAvailabilityRule, rule2: IAvailabilityRule): boolean {
    return (
      rule1.fromDate.getTime() === rule2.fromDate.getTime() &&
      rule1.toDate.getTime() === rule2.toDate.getTime() &&
      rule1.fromTime === rule2.fromTime &&
      rule1.toTime === rule2.toTime &&
      JSON.stringify(rule1.days.sort()) === JSON.stringify(rule2.days.sort())
    );
  }

  /**
   * Merge overlapping rules with minimum quantity logic
   */
  private async mergeRules(
    existingRule: IAvailabilityRule,
    newRule: IAvailabilityRule,
    session: mongoose.ClientSession
  ): Promise<IAvailabilityRule | null> {
    // For overlapping periods, use minimum quantity
    const minQuantity = Math.min(existingRule.quantityAvailable, newRule.quantityAvailable);
    
    // Archive the existing rule
    existingRule.isActive = false;
    existingRule.archivedAt = new Date();
    existingRule.archivedReason = 'Merged with new rule (minimum quantity applied)';
    await existingRule.save({ session });

    // Update new rule with minimum quantity for overlapping period
    if (minQuantity === 0) {
      newRule.reason = newRule.reason || existingRule.reason || 'Blocked due to rule merge';
    }
    newRule.quantityAvailable = minQuantity;
    
    return existingRule;
  }

  /**
   * Resolve availability for a booking query
   */
  async resolveAvailability(
    unitTypeId: string,
    startDateTime: Date,
    endDateTime: Date
  ): Promise<AvailabilityResolutionResult> {
    // Get unit type for fallback capacity
    const unitType = await UnitTypeModel.findById(unitTypeId);
    if (!unitType) {
      throw new Error('Unit type not found');
    }

    const hourlyBreakdown: AvailabilityResolutionResult['hourlyBreakdown'] = [];
    let minAvailability = unitType.totalUnits;
    let blockReason: string | undefined;

    // Generate hourly slots
    const currentHour = new Date(startDateTime);
    currentHour.setMinutes(0, 0, 0); // Round to hour

    while (currentHour < endDateTime) {
      const nextHour = new Date(currentHour);
      nextHour.setHours(currentHour.getHours() + 1);

      // Find applicable rule for this hour
      const applicableRule = await this.findApplicableRule(unitTypeId, currentHour);
      
      // Get confirmed bookings for this hour
      const confirmedBookings = await this.getConfirmedBookingsCount(unitTypeId, currentHour, nextHour);
      
      // Calculate availability
      const ruleQuantity = applicableRule?.quantityAvailable ?? unitType.totalUnits;
      const availableUnits = Math.max(0, ruleQuantity - confirmedBookings);

      hourlyBreakdown.push({
        hour: new Date(currentHour),
        totalCapacity: unitType.totalUnits,
        ruleQuantity: applicableRule?.quantityAvailable,
        confirmedBookings,
        availableUnits,
        blockReason: applicableRule?.reason
      });

      // Track minimum availability and block reason
      if (availableUnits < minAvailability) {
        minAvailability = availableUnits;
        if (availableUnits === 0 && applicableRule?.reason) {
          blockReason = applicableRule.reason;
        }
      }

      currentHour.setHours(currentHour.getHours() + 1);
    }

    return {
      available: minAvailability,
      reason: blockReason,
      hourlyBreakdown
    };
  }

  /**
   * Find the most applicable rule for a specific date and time
   */
  private async findApplicableRule(unitTypeId: string, dateTime: Date): Promise<IAvailabilityRule | null> {
    const dayName = dateTime.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
    const timeString = dateTime.toTimeString().substring(0, 5); // HH:MM format

    const rules = await AvailabilityRuleModel.find({
      unitTypeId,
      isActive: true,
      fromDate: { $lte: dateTime },
      toDate: { $gte: dateTime },
      $or: [
        { days: 'all' },
        { days: dayName }
      ],
      fromTime: { $lte: timeString },
      toTime: { $gte: timeString }
    }).sort({ createdAt: -1 }); // Most recent first

    return rules.length > 0 ? rules[0] : null;
  }

  /**
   * Get count of confirmed bookings for a specific time period
   */
  private async getConfirmedBookingsCount(
    unitTypeId: string,
    startTime: Date,
    endTime: Date
  ): Promise<number> {
    const confirmedBookings = await ReservationItemModel.countDocuments({
      unitTypeId,
      status: { $in: [ReservationStatusEnum.CONFIRMED, ReservationStatusEnum.CHECKED_IN] },
      paymentStatus: { $ne: PaymentStatusEnum.FAILED },
      startDateTime: { $lt: endTime },
      endDateTime: { $gt: startTime }
    });

    return confirmedBookings;
  }

  /**
   * Get all active rules for a unit type
   */
  async getRulesForUnitType(unitTypeId: string): Promise<IAvailabilityRule[]> {
    return await AvailabilityRuleModel.find({
      unitTypeId,
      isActive: true
    }).sort({ fromDate: 1 });
  }

  /**
   * Archive a rule
   */
  async archiveRule(ruleId: string, reason?: string): Promise<IAvailabilityRule> {
    const rule = await AvailabilityRuleModel.findById(ruleId);
    if (!rule) {
      throw new Error('Rule not found');
    }

    rule.isActive = false;
    rule.archivedAt = new Date();
    rule.archivedReason = reason || 'Manually archived';
    
    return await rule.save();
  }
}
