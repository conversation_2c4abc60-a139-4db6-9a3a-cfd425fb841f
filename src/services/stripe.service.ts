import Stripe from 'stripe';
import { IPackage, IPayment, IReservation, IUnitType } from '../types';
import { GUEST_FRONTEND_URL, STRIPE_SECRET_KEY } from '../constants';
import { LineItem } from '../types/common/stripe.types';

function getFinalAmount(amount: number) {
  return Number((amount * 100).toFixed(0));
}

export const createCheckoutSession = async (payment: IPayment, reservation: IReservation) => {
  const stripe = new Stripe(STRIPE_SECRET_KEY, { apiVersion: '2025-05-28.basil' });

  const YOUR_DOMAIN = GUEST_FRONTEND_URL;

  const lineItems: LineItem[] = [];
  const itemMap: { [key: string]: LineItem & { taxRateIds: string[] } } = {};

  for (const item of reservation.items) {
    const packageData = item.packageId as unknown as IPackage;
    const unitTypeData = item.unitTypeId as unknown as IUnitType;

    const taxRateIds: string[] = [];

    for (const tax of item.taxes) {
      const existingTaxRates = await stripe.taxRates.list({
        limit: 1000,
        active: true,
      });

      const existingTaxRate = existingTaxRates.data.find(
        (rate) => rate.percentage === parseFloat(tax.value) && rate.display_name === tax.name && !rate.inclusive,
      );

      let taxRateId: string;

      if (existingTaxRate) {
        taxRateId = existingTaxRate.id;
      } else {
        const newTaxRate = await stripe.taxRates.create({
          display_name: tax.name,
          percentage: parseFloat(tax.value),
          inclusive: false,
        });
        taxRateId = newTaxRate.id;
      }

      taxRateIds.push(taxRateId);
    }

    const itemKey = packageData.id;

    if (itemMap[itemKey]) {
      itemMap[itemKey].quantity += 1;
      itemMap[itemKey].taxRateIds = [...new Set([...itemMap[itemKey].taxRateIds, ...taxRateIds])];
      itemMap[itemKey].tax_rates = itemMap[itemKey].taxRateIds;
    } else {
      itemMap[itemKey] = {
        price_data: {
          currency: payment.currency,
          product_data: {
            name: packageData?.name || 'Room',
            description: packageData?.description || 'Room',
            images: unitTypeData?.attachments.map((attachment) => encodeURI(attachment)) ?? [],
          },
          unit_amount: getFinalAmount(item.price),
        },
        tax_rates: taxRateIds,
        taxRateIds,
        quantity: 1,
      };
    }
  }

  Object.values(itemMap).forEach((item) => {
    const { taxRateIds: _taxRateIds, ...lineItem } = item;
    lineItems.push(lineItem);
  });

  const session = await stripe.checkout.sessions.create({
    payment_method_types: ['card'],
    line_items: lineItems,
    mode: 'payment',
    success_url: `${YOUR_DOMAIN}/reservation/success?paymentId=${payment._id}`,
    cancel_url: `${YOUR_DOMAIN}/reservation/failed?paymentId=${payment._id}`,
  });

  return {
    sessionId: session.id,
    sessionUrl: session.url || '',
  };
};

export const createStripeRefund = async (sessionId: string, amount: number) => {
  const secretKey = STRIPE_SECRET_KEY;

  if (amount <= 0) {
    throw new Error('Amount should be greater than 0');
  }

  const stripe = new Stripe(secretKey, { apiVersion: '2025-05-28.basil' });

  const session = await stripe.checkout.sessions.retrieve(sessionId);
  const paymentIntentId = session.payment_intent;

  if (!paymentIntentId) {
    throw new Error('No payment intent found for this checkout session');
  }

  // const payment = await stripe.paymentIntents.retrieve(paymentIntentId);
  // if (payment.status !== 'succeeded') {
  //   throw new Error('Payment has not succeeded');
  // }

  return await stripe.refunds.create({
    payment_intent: paymentIntentId.toString(),
    amount: getFinalAmount(amount),
  });
};

export const getSessionDetails = async (sessionId: string) => {
  const stripe = new Stripe(STRIPE_SECRET_KEY);
  return await stripe.checkout.sessions.retrieve(sessionId);
};
