import { z } from 'zod';
import PaymentProviderEnum from '../types/enums/payment-provider.enum';

// Phone schema
const phoneSchema = z.object({
  countryCode: z.string().min(1, 'Country code is required'),
  phoneNumber: z.string().min(1, 'Phone number is required'),
});

// Flight details schema
const flightDetailsSchema = z.object({
  number: z.string().optional().default(''),
  from: z.string().optional().default(''),
  to: z.string().optional().default(''),
  arrivalDateTime: z.coerce.date().optional(),
  departureDateTime: z.coerce.date().optional(),
});

// Guest details schema
const guestDetailsSchema = z.object({
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  gender: z.enum(['male', 'female', 'other']).optional(),
  email: z.email('Invalid email format').optional(),
  phone: phoneSchema.optional(),
});

// Booker details schema
const bookerDetailsSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  email: z.string().email('Valid email is required'),
  phone: phoneSchema,
  gender: z.enum(['male', 'female', 'other']),
});

// Reservation item schema for creation
const reservationItemSchema = z.object({
  packageId: z.string().min(1, 'Package ID is required'),
  unitTypeId: z.string().optional(),
  startDateTime: z.coerce.date(),
  endDateTime: z.coerce.date(),
  noOfAdults: z.number().min(1, 'At least one adult is required').default(1),
  noOfChildren: z.number().min(0, 'Number of children cannot be negative').default(0),
  couponDiscount: z.number().min(0, 'Coupon discount cannot be negative').default(0),
  guestDetails: z.array(guestDetailsSchema).optional().default([]),
  specialRequest: z.string().optional().default(''),
  flightDetails: flightDetailsSchema.optional(),
});

// Get OTP validation schema
export const getOtpValidationSchema = z.object({
  reservationCode: z.string().min(1, 'Reservation code is required'),
});

// Verify OTP validation schema
export const verifyOtpValidationSchema = z.object({
  reservationCode: z.string().min(1, 'Reservation code is required'),
  otp: z.string().min(1, 'OTP is required'),
});

// Reservation creation schema
export const createReservationSchema = z.object({
  propertyId: z.string().min(1, 'Property ID is required'),
  currency: z.string().min(1, 'Currency is required'),
  paymentMethod: z.string().min(1, 'Payment method is required'),
  paymentProvider: z.enum(PaymentProviderEnum),
  reservations: z.array(reservationItemSchema).min(1, 'At least one reservation item is required'),
  bookerDetails: bookerDetailsSchema,
  paymentStatus: z.enum(['pending', 'paid', 'failed', 'refunded']).optional().default('pending'),
});

// Reservation update schema
export const updateValidationSchema = z.object({
  guestDetails: z.array(guestDetailsSchema).optional(),
});

export type GetOtpInput = z.infer<typeof getOtpValidationSchema>;
export type VerifyOtpInput = z.infer<typeof verifyOtpValidationSchema>;
export type ReservationUpdateInput = z.infer<typeof updateValidationSchema>;
export type CreateReservationInput = z.infer<typeof createReservationSchema>;
export type ReservationItemInput = z.infer<typeof reservationItemSchema>;
export type BookerDetailsInput = z.infer<typeof bookerDetailsSchema>;
export type FlightDetailsInput = z.infer<typeof flightDetailsSchema>;
