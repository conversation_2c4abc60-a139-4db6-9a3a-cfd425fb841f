import { z } from 'zod';

// Single room availability schema
const roomAvailabilityItemSchema = z.object({
  propertyId: z.string().min(1, 'Property ID is required'),
  unitTypeId: z.string().min(1, 'Unit type is required'),
  dateTime: z.coerce.date(),
  availability: z.number().min(0, 'Availability must be non-negative'),
});

// Room availability array schema
export const roomAvailabilitySchema = z
  .array(roomAvailabilityItemSchema)
  .min(1, 'At least one room availability is required');

export type RoomAvailabilityInput = z.infer<typeof roomAvailabilityItemSchema>;
export type RoomAvailabilityArrayInput = z.infer<typeof roomAvailabilitySchema>;
