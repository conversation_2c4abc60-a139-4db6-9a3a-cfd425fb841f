import { z } from 'zod';

// Valid days enum
const validDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday', 'all'] as const;

// Time format validation (HH:MM in 24-hour format)
const timeSchema = z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, {
  message: 'Time must be in HH:MM format (24-hour)'
});

// Days validation
const daysSchema = z.array(z.enum(validDays)).min(1, 'At least one day must be specified');

// Legacy single room availability schema (keeping for backward compatibility)
const roomAvailabilityItemSchema = z.object({
  propertyId: z.string().min(1, 'Property ID is required'),
  unitTypeId: z.string().min(1, 'Unit type is required'),
  dateTime: z.coerce.date().optional(),
  availability: z.number().min(0, 'Availability must be non-negative').optional(),

  // New rule-based fields
  fromDate: z.coerce.date().optional(),
  toDate: z.coerce.date().optional(),
  days: daysSchema.optional(),
  fromTime: timeSchema.optional(),
  toTime: timeSchema.optional(),
  quantityAvailable: z.number().min(0, 'Quantity available cannot be negative').optional(),
  reason: z.string().optional(),
  isActive: z.boolean().optional(),
  version: z.number().optional(),
}).refine(data => {
  // Legacy validation: if dateTime is provided, availability is required
  if (data.dateTime && data.availability === undefined) {
    return false;
  }
  // Rule-based validation: if fromDate is provided, required fields must be present
  if (data.fromDate && (!data.toDate || !data.days || data.quantityAvailable === undefined)) {
    return false;
  }
  return true;
}, {
  message: 'Either provide legacy fields (dateTime, availability) or rule-based fields (fromDate, toDate, days, quantityAvailable)'
}).refine(data => {
  // Validate date range for rule-based
  if (data.fromDate && data.toDate) {
    return data.fromDate <= data.toDate;
  }
  return true;
}, {
  message: 'fromDate must be less than or equal to toDate'
}).refine(data => {
  // Validate time range for rule-based
  if (data.fromTime && data.toTime && (data.fromTime !== '00:00' || data.toTime !== '23:59')) {
    const fromMinutes = data.fromTime.split(':').reduce((acc, time) => (60 * acc) + +time, 0);
    const toMinutes = data.toTime.split(':').reduce((acc, time) => (60 * acc) + +time, 0);
    return fromMinutes < toMinutes;
  }
  return true;
}, {
  message: 'fromTime must be less than toTime'
});

// Availability rule creation schema
export const createAvailabilityRuleSchema = z.object({
  unitTypeId: z.string().min(1, 'Unit type ID is required'),
  fromDate: z.coerce.date(),
  toDate: z.coerce.date(),
  days: daysSchema,
  fromTime: timeSchema.optional().default('00:00'),
  toTime: timeSchema.optional().default('23:59'),
  quantityAvailable: z.number().min(0, 'Quantity available cannot be negative'),
  reason: z.string().optional()
}).refine(data => data.fromDate <= data.toDate, {
  message: 'fromDate must be less than or equal to toDate',
  path: ['toDate']
}).refine(data => {
  if (data.fromTime !== '00:00' || data.toTime !== '23:59') {
    const fromMinutes = data.fromTime.split(':').reduce((acc, time) => (60 * acc) + +time, 0);
    const toMinutes = data.toTime.split(':').reduce((acc, time) => (60 * acc) + +time, 0);
    return fromMinutes < toMinutes;
  }
  return true;
}, {
  message: 'fromTime must be less than toTime',
  path: ['toTime']
});

// Quick block creation schema
export const createQuickBlockSchema = z.object({
  unitTypeId: z.string().min(1, 'Unit type ID is required'),
  date: z.coerce.date(),
  fromTime: timeSchema.optional().default('00:00'),
  toTime: timeSchema.optional().default('23:59'),
  reason: z.string().min(1, 'Reason is required for blocking')
}).refine(data => {
  if (data.fromTime !== '00:00' || data.toTime !== '23:59') {
    const fromMinutes = data.fromTime.split(':').reduce((acc, time) => (60 * acc) + +time, 0);
    const toMinutes = data.toTime.split(':').reduce((acc, time) => (60 * acc) + +time, 0);
    return fromMinutes < toMinutes;
  }
  return true;
}, {
  message: 'fromTime must be less than toTime',
  path: ['toTime']
});

// Availability resolution query schema
export const availabilityResolutionQuerySchema = z.object({
  unitTypeId: z.string().min(1, 'Unit type ID is required'),
  startDateTime: z.coerce.date(),
  endDateTime: z.coerce.date()
}).refine(data => data.startDateTime < data.endDateTime, {
  message: 'startDateTime must be before endDateTime',
  path: ['endDateTime']
});

// Room availability array schema (updated to support both legacy and rule-based)
export const roomAvailabilitySchema = z
  .array(roomAvailabilityItemSchema)
  .min(1, 'At least one room availability is required');

// Type exports
export type RoomAvailabilityInput = z.infer<typeof roomAvailabilityItemSchema>;
export type RoomAvailabilityArrayInput = z.infer<typeof roomAvailabilitySchema>;
export type CreateAvailabilityRuleInput = z.infer<typeof createAvailabilityRuleSchema>;
export type CreateQuickBlockInput = z.infer<typeof createQuickBlockSchema>;
export type AvailabilityResolutionQueryInput = z.infer<typeof availabilityResolutionQuerySchema>;
