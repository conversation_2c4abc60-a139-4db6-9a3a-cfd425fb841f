import { z } from 'zod';
import { ValidDaysEnum } from '../types';

const timeSchema = z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, {
  message: 'Time must be in HH:MM format (24-hour)',
});

const daysSchema = z.array(z.enum(ValidDaysEnum)).min(1, 'At least one day must be specified');

export const rateCardSchema = z
  .object({
    unitId: z.string().optional(),
    fromDate: z.coerce.date().optional(),
    toDate: z.coerce.date().optional(),
    days: daysSchema.optional(),
    fromTime: timeSchema.optional(),
    toTime: timeSchema.optional(),
    percentageChange: z.number().min(-100, 'Percentage change cannot be less than -100%').optional(),
    isActive: z.boolean().optional(),
    version: z.number().optional(),
  })
  .refine(
    (data) => {
      if (data.fromDate && data.toDate) {
        return data.fromDate <= data.toDate;
      }
      return true;
    },
    {
      message: 'fromDate must be less than or equal to toDate',
    },
  )
  .refine(
    (data) => {
      if (data.fromTime && data.toTime && (data.fromTime !== '00:00' || data.toTime !== '23:59')) {
        const fromMinutes = data.fromTime.split(':').reduce((acc, time) => 60 * acc + +time, 0);
        const toMinutes = data.toTime.split(':').reduce((acc, time) => 60 * acc + +time, 0);
        return fromMinutes < toMinutes;
      }
      return true;
    },
    {
      message: 'fromTime must be less than toTime',
    },
  );

// Rate card rule creation schema
export const createRateCardRuleSchema = z
  .object({
    unitId: z.string().min(1, 'Unit ID is required'),
    fromDate: z.coerce.date(),
    toDate: z.coerce.date(),
    days: daysSchema,
    fromTime: timeSchema.optional().default('00:00'),
    toTime: timeSchema.optional().default('23:59'),
    percentageChange: z.number().min(-100, 'Percentage change cannot be less than -100%'),
  })
  .refine((data) => data.fromDate <= data.toDate, {
    message: 'fromDate must be less than or equal to toDate',
    path: ['toDate'],
  })
  .refine(
    (data) => {
      if (data.fromTime !== '00:00' || data.toTime !== '23:59') {
        const fromMinutes = data.fromTime.split(':').reduce((acc, time) => 60 * acc + +time, 0);
        const toMinutes = data.toTime.split(':').reduce((acc, time) => 60 * acc + +time, 0);
        return fromMinutes < toMinutes;
      }
      return true;
    },
    {
      message: 'fromTime must be less than toTime',
      path: ['toTime'],
    },
  );

// Rate resolution query schema
export const rateResolutionQuerySchema = z
  .object({
    unitId: z.string().min(1, 'Unit ID is required'),
    packageId: z.string().min(1, 'Package ID is required'),
    startDateTime: z.coerce.date(),
    endDateTime: z.coerce.date(),
  })
  .refine((data) => data.startDateTime < data.endDateTime, {
    message: 'startDateTime must be before endDateTime',
    path: ['endDateTime'],
  });

// Type exports
export type RateCardInput = z.infer<typeof rateCardSchema>;
export type CreateRateCardRuleInput = z.infer<typeof createRateCardRuleSchema>;
export type RateResolutionQueryInput = z.infer<typeof rateResolutionQuerySchema>;
