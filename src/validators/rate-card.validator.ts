import { z } from 'zod';

// Valid days enum
const validDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday', 'all'] as const;

// Time format validation (HH:MM in 24-hour format)
const timeSchema = z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, {
  message: 'Time must be in HH:MM format (24-hour)'
});

// Days validation
const daysSchema = z.array(z.enum(validDays)).min(1, 'At least one day must be specified');

// Single rate card schema (updated to support both legacy and rule-based)
const rateCardItemSchema = z.object({
  // Legacy fields (optional for rule-based system)
  name: z.string().optional(),
  description: z.string().optional().or(z.literal('')),
  propertyId: z.string().min(1, 'Property ID is required'),
  dateTime: z.coerce.date().optional(),
  packageId: z.string().optional(),
  price: z.number().min(0, 'Price must be a positive number').optional(),

  // New rule-based fields
  unitId: z.string().optional(),
  fromDate: z.coerce.date().optional(),
  toDate: z.coerce.date().optional(),
  days: daysSchema.optional(),
  fromTime: timeSchema.optional(),
  toTime: timeSchema.optional(),
  percentageChange: z.number().min(-100, 'Percentage change cannot be less than -100%').optional(),
  isActive: z.boolean().optional(),
  version: z.number().optional(),
}).refine(data => {
  // Legacy validation: if dateTime is provided, required legacy fields must be present
  if (data.dateTime && (!data.packageId || data.price === undefined)) {
    return false;
  }
  // Rule-based validation: if unitId is provided, required rule fields must be present
  if (data.unitId && (!data.fromDate || !data.toDate || !data.days || data.percentageChange === undefined)) {
    return false;
  }
  return true;
}, {
  message: 'Either provide legacy fields (dateTime, packageId, price) or rule-based fields (unitId, fromDate, toDate, days, percentageChange)'
}).refine(data => {
  // Validate date range for rule-based
  if (data.fromDate && data.toDate) {
    return data.fromDate <= data.toDate;
  }
  return true;
}, {
  message: 'fromDate must be less than or equal to toDate'
}).refine(data => {
  // Validate time range for rule-based
  if (data.fromTime && data.toTime && (data.fromTime !== '00:00' || data.toTime !== '23:59')) {
    const fromMinutes = data.fromTime.split(':').reduce((acc, time) => (60 * acc) + +time, 0);
    const toMinutes = data.toTime.split(':').reduce((acc, time) => (60 * acc) + +time, 0);
    return fromMinutes < toMinutes;
  }
  return true;
}, {
  message: 'fromTime must be less than toTime'
});

// Rate card rule creation schema
export const createRateCardRuleSchema = z.object({
  unitId: z.string().min(1, 'Unit ID is required'),
  fromDate: z.coerce.date(),
  toDate: z.coerce.date(),
  days: daysSchema,
  fromTime: timeSchema.optional().default('00:00'),
  toTime: timeSchema.optional().default('23:59'),
  percentageChange: z.number().min(-100, 'Percentage change cannot be less than -100%')
}).refine(data => data.fromDate <= data.toDate, {
  message: 'fromDate must be less than or equal to toDate',
  path: ['toDate']
}).refine(data => {
  if (data.fromTime !== '00:00' || data.toTime !== '23:59') {
    const fromMinutes = data.fromTime.split(':').reduce((acc, time) => (60 * acc) + +time, 0);
    const toMinutes = data.toTime.split(':').reduce((acc, time) => (60 * acc) + +time, 0);
    return fromMinutes < toMinutes;
  }
  return true;
}, {
  message: 'fromTime must be less than toTime',
  path: ['toTime']
});

// Rate resolution query schema
export const rateResolutionQuerySchema = z.object({
  unitId: z.string().min(1, 'Unit ID is required'),
  packageId: z.string().min(1, 'Package ID is required'),
  startDateTime: z.coerce.date(),
  endDateTime: z.coerce.date()
}).refine(data => data.startDateTime < data.endDateTime, {
  message: 'startDateTime must be before endDateTime',
  path: ['endDateTime']
});

// Rate card array schema (updated to support both legacy and rule-based)
export const rateCardSchema = z.array(rateCardItemSchema).min(1, 'At least one rate card is required');

// Type exports
export type RateCardInput = z.infer<typeof rateCardItemSchema>;
export type RateCardArrayInput = z.infer<typeof rateCardSchema>;
export type CreateRateCardRuleInput = z.infer<typeof createRateCardRuleSchema>;
export type RateResolutionQueryInput = z.infer<typeof rateResolutionQuerySchema>;
