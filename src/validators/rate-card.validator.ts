import { z } from 'zod';

// Single rate card schema
const rateCardItemSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional().or(z.literal('')),
  propertyId: z.string().min(1, 'Property ID is required'),
  dateTime: z.coerce.date(),
  packageId: z.string().min(1, 'Package ID is required'),
  price: z.number().min(0, 'Price must be a positive number'),
});

// Rate card array schema
export const rateCardSchema = z.array(rateCardItemSchema).min(1, 'At least one rate card is required');

export type RateCardInput = z.infer<typeof rateCardItemSchema>;
export type RateCardArrayInput = z.infer<typeof rateCardSchema>;
