import { z } from 'zod';
import ServiceTypeEnum from '../types/enums/service-types.enum';

// Common unit type fields
const commonCreateFields = {
  code: z.string().min(1, 'Code is required'),
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  totalUnits: z.number().default(1).optional(),
  attachments: z.array(z.string()),
  bufferTime: z.number().default(0),
};

// Common create schema
export const commonCreateFieldsSchema = z.object(commonCreateFields);

// Unit type create schema (hotel)
export const unitTypeCreateSchema = commonCreateFieldsSchema.extend({
  capacity: z.number().min(1, 'Capacity must be at least 1'),
  bathroomType: z.string().optional(),
  isSmokingAllowed: z.boolean().optional(),
  bedType: z.string().min(1, 'Bed type is required'),
  area: z.number().min(1, 'Area must be at least 1'),
});

// Capsule type create schema
export const capsuleTypeCreateSchema = commonCreateFieldsSchema.extend({
  bedType: z.string().min(1, 'Bed type is required'),
  capacity: z.number().min(1, 'Capacity must be at least 1'),
});

// Workspace type create schema
export const workspaceTypeCreateSchema = commonCreateFieldsSchema.extend({
  capacity: z.number().min(1, 'Capacity must be at least 1'),
  area: z.number().min(1, 'Area must be at least 1'),
});

// Shower type create schema
export const showerTypeCreateSchema = commonCreateFieldsSchema.extend({
  capacity: z.number().min(1, 'Capacity must be at least 1'),
  area: z.number().min(1, 'Area must be at least 1'),
});

export const loungeTypeCreateSchema = commonCreateFieldsSchema.extend({
  area: z.number().min(1, 'Capacity must be at least 1'),
  capacity: z.number().min(1, 'Capacity must be at least 1'),
});

export const meetAndGreetTypeCreateSchema = commonCreateFieldsSchema.extend({
  capacity: z.number().min(1, 'Capacity must be at least 1'),
});

// Common update fields
const commonUpdateFields = {
  code: z.string().optional(),
  name: z.string().optional(),
  description: z.string().optional(),
  totalUnits: z.number().optional(),
  attachments: z.array(z.string()).optional(),
  bufferTime: z.number().optional(),
  active: z.boolean().optional(),
};

// Common update schema
export const commonUpdateFieldsSchema = z.object(commonUpdateFields);

// Unit type update schema
export const unitTypeUpdateSchema = commonUpdateFieldsSchema.extend({
  capacity: z.number().optional(),
  bathroomType: z.string().optional(),
  isSmokingAllowed: z.boolean().optional(),
  bedType: z.string().optional(),
  area: z.number().optional(),
});

// Capsule type update schema
export const capsuleTypeUpdateSchema = commonUpdateFieldsSchema.extend({
  bedType: z.string().optional(),
  capacity: z.number().optional(),
});

// Workspace type update schema
export const workspaceTypeUpdateSchema = commonUpdateFieldsSchema.extend({
  capacity: z.number().optional(),
  area: z.number().optional(),
});

// Shower type update schema
export const showerTypeUpdateSchema = commonUpdateFieldsSchema.extend({
  capacity: z.number().optional(),
  area: z.number().optional(),
});

export const loungeTypeUpdateSchema = commonUpdateFieldsSchema.extend({
  capacity: z.number().optional(),
  area: z.number().optional(),
});

export const meetAndGreetTypeUpdateSchema = commonUpdateFieldsSchema.extend({
  capacity: z.number().optional(),
});

export const getUnitTypeCreateSchema = (serviceTypeName: string) => {
  switch (serviceTypeName.toLowerCase()) {
    case ServiceTypeEnum.HOURLY_STAYS:
      return unitTypeCreateSchema;
    case ServiceTypeEnum.CAPSULE:
      return capsuleTypeCreateSchema;
    case ServiceTypeEnum.WORKSPACE:
      return workspaceTypeCreateSchema;
    case ServiceTypeEnum.SHOWER:
      return showerTypeCreateSchema;
    case ServiceTypeEnum.LOUNGE:
      return loungeTypeCreateSchema;
    case ServiceTypeEnum.MEET_AND_GREET:
      return meetAndGreetTypeCreateSchema;
    default:
      return commonCreateFieldsSchema;
  }
};

export const getUnitTypeUpdateSchema = (serviceTypeName: string) => {
  switch (serviceTypeName.toLowerCase()) {
    case ServiceTypeEnum.HOURLY_STAYS:
      return unitTypeUpdateSchema;
    case ServiceTypeEnum.CAPSULE:
      return capsuleTypeUpdateSchema;
    case ServiceTypeEnum.WORKSPACE:
      return workspaceTypeUpdateSchema;
    case ServiceTypeEnum.SHOWER:
      return showerTypeUpdateSchema;
    case ServiceTypeEnum.LOUNGE:
      return loungeTypeUpdateSchema;
    case ServiceTypeEnum.MEET_AND_GREET:
      return meetAndGreetTypeUpdateSchema;
    default:
      return commonUpdateFieldsSchema;
  }
};

export type CommonUnitTypeInput = z.infer<typeof commonCreateFieldsSchema>;
export type UnitTypeInput = z.infer<typeof unitTypeCreateSchema>;
export type CapsuleTypeInput = z.infer<typeof capsuleTypeCreateSchema>;
export type WorkspaceTypeInput = z.infer<typeof workspaceTypeCreateSchema>;
export type ShowerTypeInput = z.infer<typeof showerTypeCreateSchema>;
export type CommonUnitTypeUpdateInput = z.infer<typeof commonUpdateFieldsSchema>;
export type UnitTypeUpdateInput = z.infer<typeof unitTypeUpdateSchema>;
export type CapsuleTypeUpdateInput = z.infer<typeof capsuleTypeUpdateSchema>;
export type WorkspaceTypeUpdateInput = z.infer<typeof workspaceTypeUpdateSchema>;
export type ShowerTypeUpdateInput = z.infer<typeof showerTypeUpdateSchema>;
export type LoungeTypeInput = z.infer<typeof loungeTypeCreateSchema>;
export type LoungeTypeUpdateInput = z.infer<typeof loungeTypeUpdateSchema>;
export type MeetAndGreetTypeInput = z.infer<typeof meetAndGreetTypeCreateSchema>;
export type MeetAndGreetTypeUpdateInput = z.infer<typeof meetAndGreetTypeUpdateSchema>;
