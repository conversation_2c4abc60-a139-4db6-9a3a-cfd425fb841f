import { z } from 'zod';

export const paymentSchema = z.object({
  amount: z.number().optional(),
  currency: z.string().min(1, 'Currency is required'),
  reservationId: z.string().min(1, 'Reservation ID is required'),
  propertyId: z.string().min(1, 'Property ID is required'),
  status: z.string().min(1, 'Status is required'),
  paymentMethod: z.string().min(1, 'Payment method is required'),
  paymentGateway: z.string().optional(),
  paymentGatewayResponse: z.string().optional(),
});

export type PaymentInput = z.infer<typeof paymentSchema>;
