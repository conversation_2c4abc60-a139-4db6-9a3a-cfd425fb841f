import { z } from 'zod';
import { MonthsEnum } from '../types';

// Personal ID proof schema
const personalIdProofSchema = z.object({
  idType: z.string().min(1, 'ID type is required'),
  idProof: z.array(z.string()).min(1, 'At least one ID proof is required'),
  idName: z.string().min(1, 'ID name is required'),
});

// Sales tax schema
const salesTaxSchema = z.object({
  name: z.string().min(1, 'Sales tax name is required'),
  taxId: z.string().min(1, 'Sales tax ID is required'),
});

// Commission schema
const commissionSchema = z.object({
  percentage: z
    .number()
    .min(0, 'Commission percentage must be non-negative')
    .max(100, 'Commission percentage cannot exceed 100'),
  frequency: z.string().min(1, 'Frequency is required'),
});

// Business details create schema
export const createBusinessDetailsSchema = z.object({
  registrationNumber: z.string().min(1, 'Registration number is required'),
  businessTaxId: z.string().min(1, 'Business tax ID is required'),
  financialYearStart: z.enum(MonthsEnum),
  financialYearEnd: z.enum(MonthsEnum),
  registrationDocuments: z.array(z.string()).min(1, 'At least one registration document is required'),
  userPersonalId: personalIdProofSchema,
  salesTax: z.array(salesTaxSchema).default([]),
  commission: commissionSchema,
  serviceCharges: z.array(z.string()).default([]),
});

// Business details update schema
export const updateBusinessDetailsSchema = z.object({
  registrationNumber: z.string().optional(),
  businessTaxId: z.string().optional(),
  financialYearStart: z.coerce.date().optional(),
  financialYearEnd: z.coerce.date().optional(),
  registrationDocuments: z.array(z.string()).optional(),
  userPersonalId: personalIdProofSchema.optional(),
  salesTax: z.array(salesTaxSchema).optional(),
  commission: commissionSchema.optional(),
  serviceCharges: z.array(z.string()).optional(),
});

export type CreateBusinessDetailsInput = z.infer<typeof createBusinessDetailsSchema>;
export type UpdateBusinessDetailsInput = z.infer<typeof updateBusinessDetailsSchema>;
