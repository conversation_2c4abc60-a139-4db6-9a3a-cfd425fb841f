import { z } from 'zod';

// Icon schema
const iconSchema = z.object({
  web: z.string().optional(),
  mobile: z.string().optional(),
});

// Icon schema for updates (allows empty strings)
const iconUpdateSchema = z.object({
  web: z.string().optional().or(z.literal('')),
  mobile: z.string().optional().or(z.literal('')),
});

// Amenity creation schema
export const createAmenitySchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  icon: iconSchema.optional(),
  categoryId: z.string().min(1, 'Category ID is required'),
  serviceTypeId: z.string().min(1, 'Service type ID is required'),
});

// Amenity update schema
export const updateAmenitySchema = z.object({
  name: z.string().optional(),
  description: z.string().optional().or(z.literal('')),
  icon: iconUpdateSchema.optional(),
  categoryId: z.string().optional(),
  serviceTypeId: z.string().optional(),
});

export type CreateAmenityInput = z.infer<typeof createAmenitySchema>;
export type UpdateAmenityInput = z.infer<typeof updateAmenitySchema>;
