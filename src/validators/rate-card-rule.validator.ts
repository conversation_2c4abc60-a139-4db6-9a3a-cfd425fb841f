import { z } from 'zod';

// Valid days enum
const validDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday', 'all'] as const;

// Time format validation (HH:MM in 24-hour format)
const timeSchema = z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, {
  message: 'Time must be in HH:MM format (24-hour)'
});

// Days validation
const daysSchema = z.array(z.enum(validDays)).min(1, 'At least one day must be specified');

// Rate card rule creation schema
export const createRateCardRuleSchema = z.object({
  unitId: z.string().min(1, 'Unit ID is required'),
  fromDate: z.coerce.date(),
  toDate: z.coerce.date(),
  days: daysSchema,
  fromTime: timeSchema.optional().default('00:00'),
  toTime: timeSchema.optional().default('23:59'),
  percentageChange: z.number().min(-100, 'Percentage change cannot be less than -100%')
}).refine(data => data.fromDate <= data.toDate, {
  message: 'fromDate must be less than or equal to toDate',
  path: ['toDate']
}).refine(data => {
  // Validate time range if not full day
  if (data.fromTime !== '00:00' || data.toTime !== '23:59') {
    const fromMinutes = data.fromTime.split(':').reduce((acc, time) => (60 * acc) + +time, 0);
    const toMinutes = data.toTime.split(':').reduce((acc, time) => (60 * acc) + +time, 0);
    return fromMinutes < toMinutes;
  }
  return true;
}, {
  message: 'fromTime must be less than toTime',
  path: ['toTime']
});

// Rate resolution query schema
export const rateResolutionQuerySchema = z.object({
  unitId: z.string().min(1, 'Unit ID is required'),
  packageId: z.string().min(1, 'Package ID is required'),
  startDateTime: z.coerce.date(),
  endDateTime: z.coerce.date()
}).refine(data => data.startDateTime < data.endDateTime, {
  message: 'startDateTime must be before endDateTime',
  path: ['endDateTime']
});

// Archive rule schema
export const archiveRuleSchema = z.object({
  reason: z.string().optional()
});

// Unit ID parameter schema
export const unitIdParamSchema = z.object({
  unitId: z.string().min(1, 'Unit ID is required')
});

// Rule ID parameter schema
export const ruleIdParamSchema = z.object({
  ruleId: z.string().min(1, 'Rule ID is required')
});

// Type exports
export type CreateRateCardRuleInput = z.infer<typeof createRateCardRuleSchema>;
export type RateResolutionQueryInput = z.infer<typeof rateResolutionQuerySchema>;
export type ArchiveRuleInput = z.infer<typeof archiveRuleSchema>;
export type UnitIdParamInput = z.infer<typeof unitIdParamSchema>;
export type RuleIdParamInput = z.infer<typeof ruleIdParamSchema>;
