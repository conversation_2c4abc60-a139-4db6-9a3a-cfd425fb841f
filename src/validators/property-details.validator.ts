import { z } from 'zod';

// Property details create schema
export const createPropertyDetailsSchema = z.object({
  isPropertyChain: z.boolean().default(false),
  totalUnits: z.number().min(0, 'Total units must be non-negative').default(0),
  noOfReservationsPerPerson: z.number().min(0, 'Number of reservations per person must be non-negative').default(0),
  minReservationLength: z.number().min(0, 'Minimum reservation length must be non-negative').default(0),
  maxReservationLength: z.number().min(0, 'Maximum reservation length must be non-negative').default(0),
  bookingIdPrefix: z.string().min(1, 'Booking ID prefix is required'),
  invoiceIdPrefix: z.string().min(1, 'Invoice ID prefix is required'),
  frontDeskOpeningTime: z.string().min(1, 'Front desk opening time is required'),
  isOpen24Hours: z.boolean().default(false),
  aboutProperty: z.string().min(1, 'About property is required'),
  businessLogo: z.url().min(1, 'Business logo is required'),
  isAllUnitsInSameAddress: z.boolean().default(false),
  currency: z.string().min(1, 'Currency is required'),
  mailEvents: z.array(z.string()).optional(),
  frontDeskClosingTime: z.string().min(1, 'Front desk closing time is required'),
  sharedUnits: z.number().min(0, 'Shared units must be non-negative').default(0),
  privateUnits: z.number().min(0, 'Private units must be non-negative').default(0),
  isForExclusiveUse: z.boolean().default(false),
  amenities: z.array(z.string()).optional(),
  accommodationType: z.string().optional(),
  attachments: z.array(z.string()).optional(),
  areaSize: z.number().min(0, 'Area size must be non-negative').optional(),
});

// Property details update schema
export const updatePropertyDetailsSchema = z.object({
  isPropertyChain: z.boolean().optional(),
  totalUnits: z.number().min(0, 'Total units must be non-negative').optional(),
  noOfReservationsPerPerson: z.number().min(0, 'Number of reservations per person must be non-negative').optional(),
  minReservationLength: z.number().min(0, 'Minimum reservation length must be non-negative').optional(),
  maxReservationLength: z.number().min(0, 'Maximum reservation length must be non-negative').optional(),
  bookingIdPrefix: z.string(),
  invoiceIdPrefix: z.string(),
  frontDeskOpeningTime: z.string().optional(),
  isOpen24Hours: z.boolean().optional(),
  aboutProperty: z.string().optional(),
  businessLogo: z.url(),
  isAllUnitsInSameAddress: z.boolean().optional(),
  currency: z.string().optional(),
  mailEvents: z.array(z.string()).optional(),
  frontDeskClosingTime: z.string().optional(),
  sharedUnits: z.number().min(0, 'Shared units must be non-negative').optional(),
  privateUnits: z.number().min(0, 'Private units must be non-negative').optional(),
  isForExclusiveUse: z.boolean().optional(),
});

export const createPropertyAmenitiesSchema = z.object({
  amenities: z.array(z.string()).optional(),
});

export type CreatePropertyDetailsInput = z.infer<typeof createPropertyDetailsSchema>;
export type UpdatePropertyDetailsInput = z.infer<typeof updatePropertyDetailsSchema>;
export type CreatePropertyAmenitiesInput = z.infer<typeof createPropertyAmenitiesSchema>;
