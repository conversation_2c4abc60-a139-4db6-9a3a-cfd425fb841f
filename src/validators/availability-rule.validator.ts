import { z } from 'zod';

// Valid days enum
const validDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday', 'all'] as const;

// Time format validation (HH:MM in 24-hour format)
const timeSchema = z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, {
  message: 'Time must be in HH:MM format (24-hour)'
});

// Days validation
const daysSchema = z.array(z.enum(validDays)).min(1, 'At least one day must be specified');

// Availability rule creation schema
export const createAvailabilityRuleSchema = z.object({
  unitTypeId: z.string().min(1, 'Unit type ID is required'),
  fromDate: z.coerce.date(),
  toDate: z.coerce.date(),
  days: daysSchema,
  fromTime: timeSchema.optional().default('00:00'),
  toTime: timeSchema.optional().default('23:59'),
  quantityAvailable: z.number().min(0, 'Quantity available cannot be negative'),
  reason: z.string().optional()
}).refine(data => data.fromDate <= data.toDate, {
  message: 'fromDate must be less than or equal to toDate',
  path: ['toDate']
}).refine(data => {
  // Validate time range if not full day
  if (data.fromTime !== '00:00' || data.toTime !== '23:59') {
    const fromMinutes = data.fromTime.split(':').reduce((acc, time) => (60 * acc) + +time, 0);
    const toMinutes = data.toTime.split(':').reduce((acc, time) => (60 * acc) + +time, 0);
    return fromMinutes < toMinutes;
  }
  return true;
}, {
  message: 'fromTime must be less than toTime',
  path: ['toTime']
});

// Quick block creation schema
export const createQuickBlockSchema = z.object({
  unitTypeId: z.string().min(1, 'Unit type ID is required'),
  date: z.coerce.date(),
  fromTime: timeSchema.optional().default('00:00'),
  toTime: timeSchema.optional().default('23:59'),
  reason: z.string().min(1, 'Reason is required for blocking')
}).refine(data => {
  // Validate time range if not full day
  if (data.fromTime !== '00:00' || data.toTime !== '23:59') {
    const fromMinutes = data.fromTime.split(':').reduce((acc, time) => (60 * acc) + +time, 0);
    const toMinutes = data.toTime.split(':').reduce((acc, time) => (60 * acc) + +time, 0);
    return fromMinutes < toMinutes;
  }
  return true;
}, {
  message: 'fromTime must be less than toTime',
  path: ['toTime']
});

// Availability resolution query schema
export const availabilityResolutionQuerySchema = z.object({
  unitTypeId: z.string().min(1, 'Unit type ID is required'),
  startDateTime: z.coerce.date(),
  endDateTime: z.coerce.date()
}).refine(data => data.startDateTime < data.endDateTime, {
  message: 'startDateTime must be before endDateTime',
  path: ['endDateTime']
});

// Archive rule schema
export const archiveRuleSchema = z.object({
  reason: z.string().optional()
});

// Unit type ID parameter schema
export const unitTypeIdParamSchema = z.object({
  unitTypeId: z.string().min(1, 'Unit type ID is required')
});

// Rule ID parameter schema
export const ruleIdParamSchema = z.object({
  ruleId: z.string().min(1, 'Rule ID is required')
});

// Type exports
export type CreateAvailabilityRuleInput = z.infer<typeof createAvailabilityRuleSchema>;
export type CreateQuickBlockInput = z.infer<typeof createQuickBlockSchema>;
export type AvailabilityResolutionQueryInput = z.infer<typeof availabilityResolutionQuerySchema>;
export type ArchiveRuleInput = z.infer<typeof archiveRuleSchema>;
export type UnitTypeIdParamInput = z.infer<typeof unitTypeIdParamSchema>;
export type RuleIdParamInput = z.infer<typeof ruleIdParamSchema>;
