import { z } from 'zod';

// Property policy schema
const propertyPolicySchema = z.object({
  policyId: z.string().min(1, 'Policy ID is required'),
  value: z.string().min(1, 'Value is required'),
});

// Property cancellation policy schema
const propertyCancellationPolicySchema = z.object({
  hours: z.number().min(0, 'Hours must be non-negative'),
  refund_percent: z.number().min(0, 'Refund percent must be non-negative').max(100, 'Refund percent cannot exceed 100'),
  description: z.string().min(1, 'Description is required'),
});

// Property custom policy schema
const propertyCustomPolicySchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().min(1, 'Description is required'),
});

// Property policies create schema
export const createPropertyPolicySchema = z.object({
  policies: z.array(propertyPolicySchema).default([]),
  cancellationPolicies: z.array(propertyCancellationPolicySchema).default([]),
  customPolicies: z.array(propertyCustomPolicySchema).default([]),
});

// Property policies update schema
export const updatePropertyPolicySchema = z.object({
  policies: z.array(propertyPolicySchema).optional(),
  cancellationPolicies: z.array(propertyCancellationPolicySchema).optional(),
  customPolicies: z.array(propertyCustomPolicySchema).optional(),
});

export type CreatePropertyPolicyInput = z.infer<typeof createPropertyPolicySchema>;
export type UpdatePropertyPolicyInput = z.infer<typeof updatePropertyPolicySchema>;
