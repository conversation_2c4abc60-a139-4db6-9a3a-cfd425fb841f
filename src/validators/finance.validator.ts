import { z } from 'zod';

export const createPayoutSchema = z.object({
  propertyId: z.string().min(1, 'Property ID is required'),
  netPayout: z.number().min(1, 'Net payout must be at least 1'),
  date: z.coerce.date(),
  payoutUntil: z.coerce.date(),
  mode: z.string().min(1, 'Mode is required'),
  referenceNumber: z.string().min(1, 'Reference number is required'),
  attachment: z.array(z.string()).optional(),
});

export type CreatePayoutInput = z.infer<typeof createPayoutSchema>;
