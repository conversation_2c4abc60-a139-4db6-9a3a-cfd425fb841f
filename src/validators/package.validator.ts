import { z } from 'zod';
import ServiceTypeEnum from '../types/enums/service-types.enum';

// Extra bed schema
const extraBedSchema = z.object({
  available: z.boolean(),
  price: z.number().min(0, 'Extra bed price must be non-negative'),
});

// Common package fields
const commonPackageFields = {
  unitTypeId: z.string().min(1, 'Unit type ID is required'),
  name: z.string().min(1, 'Name is required'),
  description: z.string().default('').optional(),
  duration: z.number().min(1, 'Duration must be at least 1'),
  price: z.number().min(0, 'Price must be non-negative'),
  taxes: z.array(z.string()),
  amenities: z.array(z.string()),
  code: z.string().min(1, 'Code is required'),
};

// Common create package schema
export const commonCreatePackageSchema = z.object(commonPackageFields);

// Stays package schema (hotel)
export const createStaysPackageSchema = commonCreatePackageSchema.extend({
  noOfAdults: z.number().default(1),
  noOfChildren: z.number().default(0),
  extraBed: extraBedSchema,
});

// Capsules package schema
export const createCapsulesPackageSchema = commonCreatePackageSchema.extend({
  noOfAdults: z.number().default(1),
  noOfChildren: z.number().default(0),
});

// Spa package schema
export const createSpaPackageSchema = commonCreatePackageSchema.extend({
  // TODO: Make gender required
  gender: z.string().default('male'),
});

export const createLoungePackageSchema = commonCreatePackageSchema.extend({
  noOfAdults: z.number().default(1),
  noOfChildren: z.number().default(0),
});

export const createMeetAndGreetPackageSchema = commonCreatePackageSchema.extend({
  noOfAdults: z.number().default(1),
  noOfChildren: z.number().default(0),
});

// Common update package fields
const commonUpdatePackageFields = {
  unitTypeId: z.string().optional(),
  name: z.string().optional(),
  description: z.string().default('').optional(),
  duration: z.number().min(1, 'Duration must be at least 1').optional(),
  price: z.number().min(0, 'Price must be non-negative').optional(),
  taxes: z.array(z.string()).optional(),
  amenities: z.array(z.string()).optional(),
};

// Common update package schema
export const commonUpdatePackageSchema = z.object(commonUpdatePackageFields);

// Update stays package schema
export const updateStaysPackageSchema = commonUpdatePackageSchema.extend({
  noOfAdults: z.number().optional(),
  noOfChildren: z.number().optional(),
  extraBed: extraBedSchema.optional(),
});

// Update capsules package schema
export const updateCapsulesPackageSchema = commonUpdatePackageSchema.extend({
  gender: z.string().optional(),
});

export const updateLoungePackageSchema = commonUpdatePackageSchema.extend({
  noOfAdults: z.number().optional(),
  noOfChildren: z.number().optional(),
});

export const updateMeetAndGreetPackageSchema = commonUpdatePackageSchema.extend({
  noOfAdults: z.number().optional(),
  noOfChildren: z.number().optional(),
});

// Update spa package schema
export const updateSpaPackageSchema = commonUpdatePackageSchema.extend({
  gender: z.string().optional(),
});

export const getPackageCreateSchema = (serviceTypeName: string) => {
  switch (serviceTypeName.toLowerCase()) {
    case ServiceTypeEnum.HOURLY_STAYS:
      return createStaysPackageSchema;
    case ServiceTypeEnum.CAPSULE:
      return createCapsulesPackageSchema;
    case ServiceTypeEnum.SPA:
      return createSpaPackageSchema;
    case ServiceTypeEnum.LOUNGE:
      return createLoungePackageSchema;
    case ServiceTypeEnum.MEET_AND_GREET:
      return createMeetAndGreetPackageSchema;
    default:
      return commonCreatePackageSchema;
  }
};

export const getPackageUpdateSchema = (serviceTypeName: string) => {
  switch (serviceTypeName.toLowerCase()) {
    case ServiceTypeEnum.HOURLY_STAYS:
      return updateStaysPackageSchema;
    case ServiceTypeEnum.CAPSULE:
      return updateCapsulesPackageSchema;
    case ServiceTypeEnum.SPA:
      return updateSpaPackageSchema;
    case ServiceTypeEnum.LOUNGE:
      return updateLoungePackageSchema;
    case ServiceTypeEnum.MEET_AND_GREET:
      return updateMeetAndGreetPackageSchema;
    default:
      return commonUpdatePackageSchema;
  }
};

export type CommonPackageInput = z.infer<typeof commonCreatePackageSchema>;
export type StaysPackageInput = z.infer<typeof createStaysPackageSchema>;
export type CapsulesPackageInput = z.infer<typeof createCapsulesPackageSchema>;
export type SpaPackageInput = z.infer<typeof createSpaPackageSchema>;
export type CommonPackageUpdateInput = z.infer<typeof commonUpdatePackageSchema>;
export type StaysPackageUpdateInput = z.infer<typeof updateStaysPackageSchema>;
export type CapsulesPackageUpdateInput = z.infer<typeof updateCapsulesPackageSchema>;
export type SpaPackageUpdateInput = z.infer<typeof updateSpaPackageSchema>;
export type LoungePackageInput = z.infer<typeof createLoungePackageSchema>;
export type LoungePackageUpdateInput = z.infer<typeof updateLoungePackageSchema>;
export type MeetAndGreetPackageInput = z.infer<typeof createMeetAndGreetPackageSchema>;
export type MeetAndGreetPackageUpdateInput = z.infer<typeof updateMeetAndGreetPackageSchema>;
