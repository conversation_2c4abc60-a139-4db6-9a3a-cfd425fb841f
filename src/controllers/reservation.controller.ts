import Reservation from '../models/reservation.model';
import ReservationItem from '../models/reservation-item.model';
import UnitType from '../models/unit-type.model';
import Package from '../models/package.model';
import mongoose, { Types } from 'mongoose';
import DomainValue from '../models/domain-value.model';
import UserSchema from '../models/user.model';
import { validateRefundInputs } from '../utils/cancellation';
import OTPSchema from '../models/otp-model';
import { generateRandomOTP } from '../utils/otpUtils';
import { EmailService } from '../services/email.service';
import PropertyModel from '../models/property.model';
import { checkRoomAvailability } from '../utils/availability';
import { getRateCardPrices } from '../utils/rateCard';
import {
  IDomainValue,
  IOtp,
  IPackage,
  IRequest,
  IReservation,
  IResponse,
  IUnitType,
  OtpPurposeEnum,
  ReservationStatusEnum,
  UserRoleEnum,
} from '../types';
import { ResponseUtil } from '../utils/response';
import { asyncRequestHandler } from '../middlewares/async-request-handler';
import SequenceModel from '../models/sequence.model';
import PropertyDetailsModel from '../models/property-details.model';
import PaymentModel from '../models/payment.model';
import { createCheckoutSession } from '../services/stripe.service';
import PaymentProviderEnum from '../types/enums/payment-provider.enum';
import { checkReservationAuthorization } from '../utils/reservation';

export class ReservationController {
  private static Instance: ReservationController;
  private emailService: EmailService;

  public static getInstance(): ReservationController {
    if (!ReservationController.Instance) {
      ReservationController.Instance = new ReservationController();
    }
    return ReservationController.Instance;
  }

  private constructor() {
    this.emailService = new EmailService();
  }

  getAvailable = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { propertyId } = req.params;

    if (!mongoose.Types.ObjectId.isValid(propertyId)) {
      return ResponseUtil.badRequest(res, 'Invalid property ID format');
    }

    const { duration, noOfAdults, noOfChildren, startDateTime, endDateTime } = req.query;

    const packageFilter: mongoose.FilterQuery<IPackage> = {
      propertyId: propertyId,
      active: true,
    };

    if (duration) {
      packageFilter.duration = { $lte: parseInt(duration as string, 10) };
    }

    if (noOfAdults) {
      packageFilter.noOfAdults = parseInt(noOfAdults as string, 10);
    }

    if (noOfChildren) {
      packageFilter.noOfChildren = parseInt(noOfChildren as string, 10);
    }
    if (startDateTime && endDateTime) {
      const start = new Date(startDateTime as string);
      const end = new Date(endDateTime as string);
      packageFilter.duration = {
        $lte: Math.ceil((end.getTime() - start.getTime()) / (1000 * 60)),
      };
    }

    const packages: (IPackage & {
      unitTypeId: IUnitType;
      taxes: IDomainValue[];
      amenities: IDomainValue[];
    })[] = await Package.find(
      packageFilter,
      {},
      {
        populate: [
          {
            path: 'taxes',
            select: 'name value',
          },
          {
            path: 'amenities',
            select: 'name categoryId',
            populate: [
              {
                path: 'categoryId',
                select: 'name',
              },
            ],
          },
          {
            path: 'unitTypeId',
            select: '-createdAt -updatedAt -__v -deleted -active',
          },
        ],
      },
    );

    const packageIds = packages.map((pkg) => pkg._id.toString());
    const rateCardMap = await getRateCardPrices({
      packageIds,
      startDateTime: startDateTime as string,
      endDateTime: endDateTime as string,
    });

    let updatedPackages = packages.map((pkg) => ({
      ...pkg.toObject(),
      rateCardPrice: rateCardMap[pkg._id.toString()] !== undefined ? rateCardMap[pkg._id.toString()] : null,
    }));

    if (startDateTime && endDateTime) {
      const unitTypeIds = [...new Set(packages.map((pkg) => pkg.unitTypeId._id.toString()))];

      const unitTypes = await UnitType.find({
        propertyId,
        active: true,
        _id: { $in: unitTypeIds },
      });

      const availabilityResults = await checkRoomAvailability({
        propertyId,
        unitTypes,
        startDateTime: startDateTime as string,
        endDateTime: endDateTime as string,
      });

      updatedPackages = updatedPackages.filter((pkg) => {
        const availabilityResult = availabilityResults.find(
          (result) => result.unitTypeId === pkg.unitTypeId._id.toString(),
        );
        const availableRooms = availabilityResult?.availableRooms || 0;
        return availableRooms > 0;
      });
    }

    ResponseUtil.success(res, 'Room availability retrieved successfully', updatedPackages);
  });

  block = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { reservations, bookerDetails, propertyId, currency, paymentMethod, paymentProvider } = req.body;

    if (!mongoose.Types.ObjectId.isValid(propertyId)) {
      return ResponseUtil.badRequest(res, 'Invalid ID format');
    }

    const propertyDetails = await PropertyDetailsModel.findOne({ propertyId });

    if (!propertyDetails) {
      return ResponseUtil.notFound(res, 'Property details not found');
    }

    if (!reservations || !Array.isArray(reservations) || reservations.length === 0) {
      return ResponseUtil.badRequest(res, 'No reservations provided');
    }

    const session = await mongoose.startSession();

    try {
      await session.withTransaction(async () => {
        const packageIds = reservations.map((reservation: Record<string, unknown>) => reservation.packageId);
        if (!packageIds || packageIds.length === 0) {
          throw new Error('No packageId provided');
        }

        const packages = await Package.find({
          _id: { $in: packageIds },
        })
          .lean()
          .session(session);
        const taxIds = packages.flatMap((pkg) => pkg.taxes);
        const taxes = await DomainValue.find({ _id: { $in: taxIds } })
          .lean()
          .session(session);

        if (!packages || packages.length === 0) {
          throw new Error('No packages found');
        }

        const unitTypeIds = reservations.map(
          (resp) =>
            resp.unitTypeId || packages.find((pkg) => pkg._id.toString() === resp.packageId.toString())?.unitTypeId,
        );
        const unitTypes = await UnitType.find({ _id: { $in: unitTypeIds } }).session(session);

        for (const reservation of reservations) {
          const unitType = unitTypes.find((rt) => rt._id.toString() === reservation.unitTypeId?.toString());
          if (!unitType) continue;

          const availabilityResults = await checkRoomAvailability({
            propertyId,
            unitTypes: [unitType],
            startDateTime: reservation.startDateTime.toString(),
            endDateTime: reservation.endDateTime.toString(),
          });

          const availableRooms = availabilityResults[0]?.availableRooms || 0;

          if (availableRooms <= 0) {
            throw new Error(`No rooms available for ${unitType.name} during the requested period`);
          }
        }

        const rateCardMap = await getRateCardPrices({
          packageIds: packageIds.map((id) => (id as mongoose.Types.ObjectId).toString()),
          startDateTime: reservations[0]?.startDateTime,
          endDateTime: reservations[0]?.endDateTime,
        });

        const createdReservationItems = [];
        let subTotal = 0;
        let totalTax = 0;

        for (const reservationData of reservations) {
          const pkg = packages.find((pkgData) => pkgData._id.toString() === reservationData.packageId.toString());
          const packageTax = taxes.filter((tax) => pkg?.taxes.map((t) => t.toString()).includes(tax._id.toString()));

          const price =
            rateCardMap[reservationData.packageId.toString()] !== undefined
              ? rateCardMap[reservationData.packageId.toString()]
              : pkg?.price || 0;

          const tax = packageTax.reduce((acc: number, taxItem) => {
            const taxValue = Number(taxItem.value) || 0;
            return acc + (price * taxValue) / 100;
          }, 0);

          const newReservationItem = new ReservationItem({
            propertyId: new mongoose.Types.ObjectId(propertyId),
            status: ReservationStatusEnum.BLOCKED,
            paymentStatus: req.body.paymentStatus || 'pending',
            couponDiscount: reservationData.couponDiscount || 0,
            unitTypeId: new Types.ObjectId(pkg?.unitTypeId),
            packageId: new Types.ObjectId(reservationData.packageId),
            startDateTime: new Date(reservationData.startDateTime),
            endDateTime: new Date(reservationData.endDateTime),
            noOfAdults: reservationData.noOfAdults || 1,
            noOfChildren: reservationData.noOfChildren || 0,
            price,
            taxes: packageTax || [],
            tax,
            totalAmount: price + tax,
            guestDetails: reservationData.guestDetails || [],
            specialRequest: reservationData.specialRequest || '',
            flightDetails: reservationData.flightDetails || {},
            bookerDetails,
          });

          await newReservationItem.save({ session });
          createdReservationItems.push(newReservationItem);
          subTotal += price;
          totalTax += tax;
        }

        const reservationCodeSequence = await SequenceModel.findOneAndUpdate(
          { name: 'reservationCode' },
          { $inc: { sequence_value: 1 } },
          { new: true, upsert: true, setDefaultsOnInsert: true },
        ).session(session);
        if (!reservationCodeSequence) {
          throw new Error('Failed to generate sequence number');
        }
        const reservationCode = `${propertyDetails.bookingIdPrefix}-${new Date().getFullYear().toString().slice(-2)}${(new Date().getMonth() + 1).toString().padStart(2, '0')}${new Date().getDate().toString().padStart(2, '0')}-${reservationCodeSequence.sequence_value.toString().padStart(6, '0')}`;

        const invoiceCodeSequence = await SequenceModel.findOneAndUpdate(
          { name: 'invoiceCode' },
          { $inc: { sequence_value: 1 } },
          { new: true, upsert: true, setDefaultsOnInsert: true },
        ).session(session);
        if (!invoiceCodeSequence) {
          throw new Error('Failed to generate sequence number');
        }
        const invoiceCode = `${propertyDetails.invoiceIdPrefix}-${new Date().getFullYear().toString().slice(-2)}${(new Date().getMonth() + 1).toString().padStart(2, '0')}${new Date().getDate().toString().padStart(2, '0')}-${invoiceCodeSequence.sequence_value.toString().padStart(6, '0')}`;

        const newReservation = new Reservation({
          propertyId: new mongoose.Types.ObjectId(propertyId),
          reservationCode,
          invoiceCode,
          items: createdReservationItems.map((item) => item._id),
          subTotal,
          totalTax,
          grandTotal: subTotal + totalTax,
          bookerDetails,
        });

        await newReservation.save({ session });

        await newReservation.populate({
          path: 'items',
          populate: [{ path: 'unitTypeId' }, { path: 'packageId' }, { path: 'taxes' }],
        });

        const payment = await PaymentModel.create(
          [
            {
              reservationId: newReservation._id,
              propertyId: propertyId,
              amount: newReservation.grandTotal,
              currency: currency,
              paymentMethod: paymentMethod,
            },
          ],
          { session },
        );

        if (paymentProvider === PaymentProviderEnum.STRIPE) {
          const response = await createCheckoutSession(payment[0], newReservation);
          await PaymentModel.findByIdAndUpdate(
            payment[0]._id,
            {
              paymentGateway: PaymentProviderEnum.STRIPE,
              paymentGatewayResponse: response,
            },
            { session },
          );

          return ResponseUtil.success(res, 'Checkout session created successfully', response);
        }

        return ResponseUtil.created(res, 'Booking successful', newReservation);
      });
    } catch (error) {
      console.error('Transaction failed:', error);
      return ResponseUtil.internalServerError(res, error instanceof Error ? error.message : 'Transaction failed');
    } finally {
      await session.endSession();
    }
  });

  get = asyncRequestHandler(async (req, res) => {
    const user = req.user;

    if (!user) {
      return ResponseUtil.unauthorized(res, 'Unauthorized');
    }

    const reservation = await Reservation.findById(req.params.id).populate([
      {
        path: 'items',
        populate: [{ path: 'unitTypeId' }, { path: 'packageId' }, { path: 'taxes' }],
      },
      {
        path: 'propertyId',
        populate: [{ path: 'address.locationId' }, { path: 'serviceType' }],
      },
    ]);
    if (!reservation) {
      return ResponseUtil.notFound(res, 'Reservation not found');
    }

    const authCheck = await checkReservationAuthorization(user, reservation);
    if (!authCheck.isAuthorized) {
      return ResponseUtil.unauthorized(res, authCheck.message ?? 'Unauthorized');
    }

    ResponseUtil.success(res, 'Reservation retrieved successfully', reservation);
  });

  getByPropertyId = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const userRole = req.user?.role;

    const propertyId = req.params.propertyId;
    const { q, startDate, endDate, status, paymentStatus } = req.query;

    const query: mongoose.FilterQuery<IReservation> = {};

    if (userRole && userRole === UserRoleEnum.MERCHANT) {
      const property = await PropertyModel.findById(propertyId);

      if (!property) {
        return ResponseUtil.notFound(res, 'Property not found');
      }

      query.propertyId = propertyId;
    }

    if (q && typeof q === 'string') {
      const searchConditions: mongoose.FilterQuery<IReservation>[] = [
        { reservationCode: { $regex: q, $options: 'i' } },
        { 'bookerDetails.firstName': { $regex: q, $options: 'i' } },
        { 'bookerDetails.lastName': { $regex: q, $options: 'i' } },
        { 'bookerDetails.email': { $regex: q, $options: 'i' } },
      ];

      if (userRole === UserRoleEnum.SUPERADMIN) {
        searchConditions.push({
          propertyId: {
            $in: await PropertyModel.find({ name: { $regex: q, $options: 'i' } }).distinct('_id'),
          },
        });
      }

      query.$or = searchConditions;
    }

    if (status && typeof status === 'string') {
      const statusArray = status.split(',').filter((s) => s.trim()); // Remove empty strings
      if (statusArray.length > 0) {
        query.status = { $in: statusArray };
      }
    }

    if (paymentStatus && typeof paymentStatus === 'string') {
      const paymentStatusArray = paymentStatus.split(',').filter((s) => s.trim()); // Remove empty strings
      if (paymentStatusArray.length > 0) {
        query.paymentStatus = { $in: paymentStatusArray };
      }
    }

    // Handle date range filtering
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate && typeof startDate === 'string') {
        query.createdAt.$gte = new Date(startDate);
      }
      if (endDate && typeof endDate === 'string') {
        query.createdAt.$lte = new Date(endDate);
      }
    }

    const reservations = await Reservation.find(query)
      .populate([
        {
          path: 'items',
          populate: [
            { path: 'unitTypeId', select: 'name' },
            { path: 'packageId', select: 'name' },
            { path: 'taxes', select: 'name value' },
          ],
        },
        {
          path: 'propertyId',
          select: 'name address',
          populate: [
            { path: 'address.locationId', select: 'name' },
            { path: 'serviceType', select: 'name' },
          ],
        },
      ])
      .sort({ updatedAt: -1 });

    ResponseUtil.success(res, 'Reservations retrieved successfully', reservations);
  });

  getByUser = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const userId = req.user?.userId;

    if (!userId) {
      return ResponseUtil.notFound(res, 'User not found');
    }

    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return ResponseUtil.badRequest(res, 'Invalid user ID format');
    }

    const user = await UserSchema.findById(userId);

    if (!user) {
      return ResponseUtil.notFound(res, 'User not found');
    }

    const reservations = await Reservation.find({
      'bookerDetails.email': user.email,
    })
      .populate([
        {
          path: 'items',
          populate: [
            { path: 'unitTypeId', select: 'name' },
            { path: 'packageId', select: 'name' },
            { path: 'taxes', select: 'name value' },
          ],
        },
        {
          path: 'propertyId',
          select: 'name address',
          populate: [
            {
              path: 'address.locationId',
              select: 'name',
            },
            {
              path: 'serviceType',
              select: 'name',
            },
          ],
        },
      ])
      .sort({ createdAt: -1 });

    const propertyDetails = await PropertyDetailsModel.find({
      propertyId: { $in: reservations.map((r) => r.propertyId) },
    }).lean();

    const reservationsWithPropertyDetails = reservations.map((reservation) => {
      return {
        ...reservation.toObject(),
        propertyDetails: propertyDetails.find((d) => d.propertyId.toString() === reservation.propertyId._id.toString()),
      };
    });

    ResponseUtil.success(res, 'Reservations retrieved successfully', reservationsWithPropertyDetails);
  });

  getRefundableAmount = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const user = req.user;

    if (!user) {
      return ResponseUtil.unauthorized(res, 'Unauthorized');
    }

    const { reservationId } = req.params;
    const cancellationTime = new Date().toISOString();

    const reservation = await Reservation.findById(reservationId).select('bookerDetails');

    if (!reservation) {
      return ResponseUtil.notFound(res, 'Reservation not found');
    }

    const authCheck = await checkReservationAuthorization(user, reservation);
    if (!authCheck.isAuthorized) {
      return ResponseUtil.unauthorized(res, authCheck.message ?? 'Unauthorized');
    }

    const validationResult = await validateRefundInputs(reservationId, cancellationTime as string, res);
    if (!validationResult) return;

    const { refundPolicy, refundableAmount } = validationResult;

    ResponseUtil.success(res, 'Refundable amount retrieved successfully', {
      refundableAmount,
      refundablePercentage: refundPolicy.refund_percent || 0,
      description: refundPolicy.description,
    });
  });

  getOtp = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const user = req.user;
    if (!user) {
      return ResponseUtil.unauthorized(res, 'Unauthorized');
    }
    const { reservationCode } = req.body;

    const reservation = await Reservation.findOne({ reservationCode });

    if (!reservation) {
      return ResponseUtil.notFound(res, 'Reservation not found');
    }

    const authCheck = await checkReservationAuthorization(user, reservation);
    if (!authCheck.isAuthorized) {
      return ResponseUtil.unauthorized(res, authCheck.message ?? 'Unauthorized');
    }

    const userEmail = reservation.bookerDetails.email;

    const purpose = OtpPurposeEnum.CANCEL_BOOKING;

    const otp = generateRandomOTP(6);

    const expiresAt = new Date(Date.now() + 10 * 60 * 1000);

    const existingOTP = await OTPSchema.findOne({
      userEmail,
      reservationCode,
      purpose,
    });
    if (existingOTP) {
      existingOTP.otp = otp;
      existingOTP.expiresAt = expiresAt;
      existingOTP.createdAt = new Date();
      await existingOTP.save();
    } else {
      const newOTP: IOtp = new OTPSchema({
        userEmail,
        reservationCode,
        purpose,
        otp,
        expiresAt,
      });
      await newOTP.save();
    }

    await this.emailService.sendOtpVerificationEmail(userEmail, reservation.bookerDetails.firstName, otp);

    ResponseUtil.success(res, 'OTP generated successfully');
  });

  verifyOtp = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { reservationCode, otp } = req.body;

    const existingOTP = await OTPSchema.findOne({
      reservationCode,
      purpose: OtpPurposeEnum.CANCEL_BOOKING,
      otp,
      expiresAt: { $gt: new Date() },
    });

    if (!existingOTP) {
      return ResponseUtil.badRequest(res, 'Invalid OTP');
    }

    const reservation = await Reservation.findOne({ reservationCode });

    if (!reservation) {
      return ResponseUtil.notFound(res, 'Reservation not found');
    }

    await existingOTP.deleteOne();

    ResponseUtil.success(res, 'OTP verified successfully', reservation);
  });

  update = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const user = req.user;

    if (!user) {
      return ResponseUtil.unauthorized(res, 'Unauthorized');
    }
    const reservation = await Reservation.findById(req.params.id);

    if (!reservation) {
      return ResponseUtil.notFound(res, 'Reservation not found');
    }

    const authCheck = await checkReservationAuthorization(user, reservation);
    if (!authCheck.isAuthorized) {
      return ResponseUtil.unauthorized(res, authCheck.message ?? 'Unauthorized');
    }

    for (const item of reservation.items) {
      if (new Date(item.startDateTime).getTime() < Date.now()) {
        return ResponseUtil.badRequest(res, 'Cannot update past reservation');
      }
    }

    const property = await PropertyModel.findById(reservation.propertyId).populate([
      { path: 'owner' },
      { path: 'serviceType' },
    ]);
    if (!property) {
      return ResponseUtil.notFound(res, 'Property not found');
    }

    if (req.body.guestDetails && Array.isArray(req.body.guestDetails)) {
      await ReservationItem.updateMany(
        { _id: { $in: reservation.items } },
        { $set: { guestDetails: req.body.guestDetails } },
        { runValidators: true },
      );
    }

    const updatedReservationItem = await Reservation.findById(req.params.id).populate([
      {
        path: 'items',
        populate: [
          { path: 'unitTypeId', select: 'name' },
          { path: 'packageId', select: 'name' },
          { path: 'taxes', select: 'name value' },
        ],
      },
    ]);
    if (!updatedReservationItem) {
      return ResponseUtil.notFound(res, 'Reservation not found after update');
    }

    // await this.emailService.sendUpdateReservationEmail(
    //   updatedReservationItem.bookerDetails.email,
    //   updatedReservationItem,
    //   property,
    // );

    ResponseUtil.success(res, 'Reservation updated successfully', updatedReservationItem);
  });
}
