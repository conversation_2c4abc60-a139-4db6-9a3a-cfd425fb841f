import { ILocation, IRequest, IResponse, PropertyStatusEnum } from '../types';
import Location from '../models/location.model';
import axios from 'axios';
import { ResponseUtil } from '../utils/response';
import { asyncRequestHandler } from '../middlewares/async-request-handler';
import PropertyModel from '../models/property.model';
import mongoose from 'mongoose';

export class LocationController {
  private static Instance: LocationController;

  public static getInstance(): LocationController {
    if (!LocationController.Instance) LocationController.Instance = new LocationController();
    return LocationController.Instance;
  }

  private constructor() {}

  getAll = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { search } = req.query;
    let locations: ILocation[];

    if (search) {
      const searchRegex = new RegExp(String(search), 'i');
      locations = await Location.find({
        $or: [{ code: searchRegex }, { name: searchRegex }, { city: searchRegex }],
        deleted: false,
      }).sort({
        updatedAt: -1,
      });
    } else {
      locations = await Location.find({ deleted: false }).sort({
        updatedAt: -1,
      });
    }

    ResponseUtil.success(res, 'Locations retrieved successfully', locations);
  });

  getAllAvailableLocations = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { search } = req.query;

    const properties = await PropertyModel.find({
      deleted: false,
      active: true,
      status: PropertyStatusEnum.APPROVED,
    }).select('address.locationId');
    const locationIds = properties.map((property) => property.address.locationId);

    const query: mongoose.FilterQuery<ILocation> = {
      _id: { $in: locationIds },
      deleted: false,
    };

    if (search) {
      const searchRegex = new RegExp(String(search), 'i');
      query.$or = [{ code: searchRegex }, { name: searchRegex }, { city: searchRegex }];
    }

    const locations = await Location.find(query).sort({ code: 1 }).select('code name city country latitude longitude');

    ResponseUtil.success(res, 'Locations retrieved successfully', locations);
  });

  create = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const location = await Location.create(req.body);
    ResponseUtil.created(res, 'Location created successfully', location);
  });

  update = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { id } = req.params;
    const location = await Location.findByIdAndUpdate(id, req.body, {
      new: true,
    });
    if (!location) {
      return ResponseUtil.notFound(res, 'Location not found');
    }
    ResponseUtil.success(res, 'Location updated successfully', location);
  });

  delete = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { id } = req.params;
    const location = await Location.findByIdAndUpdate(id, { deleted: true });
    if (!location) {
      return ResponseUtil.notFound(res, 'Location not found');
    }
    ResponseUtil.success(res, 'Location deleted successfully');
  });

  get = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const location = await Location.findById(req.params.id);
    if (!location) {
      return ResponseUtil.notFound(res, 'Location not found');
    }
    ResponseUtil.success(res, 'Location retrieved successfully', location);
  });

  getUserLocationDetails = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { latitude, longitude } = req.query;

    if (!latitude || !longitude) {
      const ip =
        (typeof req.headers['x-forwarded-for'] === 'string'
          ? req.headers['x-forwarded-for'].split(',')[0].trim()
          : req.socket.remoteAddress) || req.socket.remoteAddress;
      if (ip === '::1' || ip === '127.0.0.1') {
        return ResponseUtil.badRequest(res, 'Localhost is not supported');
      }
      const response = await axios.get(`http://ip-api.com/json/${ip}`);
      const { countryCode, city, region, timezone, lat, lon, zip } = response.data;
      return ResponseUtil.success(res, 'Location details retrieved successfully', {
        countryCode,
        city,
        region,
        timezone,
        latitude: lat,
        longitude: lon,
        zipCode: zip,
      });
    }

    // // Validate that coordinates are valid numbers
    const lat = parseFloat(String(latitude));
    const lng = parseFloat(String(longitude));

    const response = await axios.get(
      `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${process.env.GOOGLE_MAPS_API_KEY}`,
    );

    // Extract country code from the response
    let countryCode = null;
    if (response.data.results && response.data.results.length > 0) {
      const addressComponents = response.data.results[0].address_components;
      const countryComponent = addressComponents.find((component: { types: string[] }) =>
        component.types.includes('country'),
      );
      if (countryComponent) {
        countryCode = countryComponent.short_name;
      }
    }

    // Return the original response data along with the extracted country code
    const locationData = {
      locality: response.data.results[0].address_components.find((c: { types: string[] }) =>
        c.types.includes('locality'),
      )?.long_name,
      city: response.data.results[0].address_components.find((c: { types: string[] }) => c.types.includes('political'))
        ?.long_name,
      countryCode: countryCode,
      zipCode: response.data.results[0].address_components.find((c: { types: string[] }) =>
        c.types.includes('postal_code'),
      )?.long_name,
      latitude,
      longitude,
    };
    ResponseUtil.success(res, 'Location details retrieved successfully', locationData);
  });
}
