import {
  ILocation,
  IPackage,
  IProperty,
  IRequest,
  IResponse,
  IUnitType,
  IUser,
  PropertyStatusEnum,
  UserRoleEnum,
} from '../types';
import Property from '../models/property.model';
import Package from '../models/package.model';
import Location from '../models/location.model';
import { EmailService } from '../services/email.service';
import mongoose from 'mongoose';
import UnitTypeModel from '../models/unit-type.model';
import { checkRoomAvailability } from '../utils/availability';
import { getRateCardPrices } from '../utils/rateCard';
import { ResponseUtil } from '../utils/response';
import PropertyDetailsModel from '../models/property-details.model';
import BusinessDetailsModel from '../models/business-details.model';
import PropertyPoliciesModel from '../models/property-policies.model';
import { asyncRequestHandler } from '../middlewares/async-request-handler';
import loggerService from '../utils/logger/logger.service';

export class PropertyController {
  private static instance: PropertyController;
  private emailService: EmailService;

  private constructor() {
    this.emailService = new EmailService();
  }

  public static getInstance(): PropertyController {
    if (!PropertyController.instance) {
      PropertyController.instance = new PropertyController();
    }
    return PropertyController.instance;
  }

  getAll = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { q, status, active } = req.query;
    const propertyFilterQuery: mongoose.FilterQuery<IProperty> = {};

    if (q) {
      propertyFilterQuery.$or = [
        { name: { $regex: q, $options: 'i' } },
        { code: { $regex: q, $options: 'i' } },
        { 'address.city': { $regex: q, $options: 'i' } },
        { 'address.state': { $regex: q, $options: 'i' } },
        { 'address.country': { $regex: q, $options: 'i' } },
      ];
    }
    if (status) {
      propertyFilterQuery.status = status;
    }
    if (active === 'true') {
      propertyFilterQuery.active = true;
    }

    const properties: (IProperty & { packages?: IPackage[] })[] = await Property.find(propertyFilterQuery)
      .lean()
      .sort({ updatedAt: -1 })
      .populate([
        { path: 'address.locationId', select: 'name code' },
        { path: 'serviceType', select: 'name' },
        {
          path: 'owner',
          select: '-password -createdAt -updatedAt -__v -role',
        },
      ]);

    const propertyIds = properties.map((p) => p._id);

    const [unitTypeCounts, propertyDetails, businessDetailsCounts, propertyPoliciesCounts] = await Promise.all([
      UnitTypeModel.aggregate([
        { $match: { propertyId: { $in: propertyIds } } },
        { $group: { _id: '$propertyId', count: { $sum: 1 } } },
      ]),
      PropertyDetailsModel.find({ propertyId: { $in: propertyIds } }).lean(),
      BusinessDetailsModel.aggregate([
        { $match: { propertyId: { $in: propertyIds } } },
        { $group: { _id: '$propertyId', count: { $sum: 1 } } },
      ]),
      PropertyPoliciesModel.aggregate([
        { $match: { propertyId: { $in: propertyIds } } },
        { $group: { _id: '$propertyId', count: { $sum: 1 } } },
      ]),
    ]);

    // build lookup maps
    const unitTypeMap = new Map(unitTypeCounts.map((u) => [u._id.toString(), u.count]));
    const propertyDetailsMap = new Map(propertyDetails.map((d) => [d.propertyId.toString(), d]));
    const businessDetailsMap = new Map(businessDetailsCounts.map((b) => [b._id.toString(), b.count]));
    const propertyPoliciesMap = new Map(propertyPoliciesCounts.map((p) => [p._id.toString(), p.count]));

    const finalData = properties.map((property) => {
      const pid = property._id.toString();
      const hasUnitTypes = (unitTypeMap.get(pid) || 0) > 0;
      const details = propertyDetailsMap.get(pid);
      const hasPropertyDetails = !!details;
      const hasBusinessDetails = (businessDetailsMap.get(pid) || 0) > 0;
      const hasPropertyPolicies = (propertyPoliciesMap.get(pid) || 0) > 0;
      const hasAmenities = !!details && Array.isArray(details.amenities) && details.amenities.length > 0;

      const hasFilledData =
        hasUnitTypes && hasPropertyDetails && hasBusinessDetails && hasPropertyPolicies && hasAmenities;

      return {
        ...property,
        hasFilledData,
      };
    });

    ResponseUtil.success(res, 'Properties retrieved successfully', finalData);
  });

  search = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const propertyFilterQuery: mongoose.FilterQuery<IProperty> = { active: true };
    const {
      city,
      locationId,
      duration,
      noOfAdults,
      noOfChildren,
      startDateTime,
      endDateTime,
      premisesType,
      serviceTypeId,
    } = req.query as {
      city?: string;
      locationId?: string;
      duration?: string;
      noOfAdults?: string;
      noOfChildren?: string;
      startDateTime?: string;
      endDateTime?: string;
      premisesType?: string;
      serviceTypeId?: string;
    };

    // --- Build property filter ---
    if (locationId) {
      const location = await Location.findById(locationId);
      if (location) {
        const allCityLocations: ILocation[] = await Location.find({ city: location.city });
        propertyFilterQuery['address.locationId'] = { $in: allCityLocations.map((l) => l._id) };
      }
    } else if (city) {
      propertyFilterQuery['address.city'] = city;
    }

    if (premisesType) {
      propertyFilterQuery.premisesType = premisesType;
    }
    if (serviceTypeId) {
      const seperatedServiceTypeId = serviceTypeId.split(',');
      propertyFilterQuery.serviceType = { $in: seperatedServiceTypeId.map((id) => new mongoose.Types.ObjectId(id)) };
    }

    // --- Fetch properties ---
    const properties: (IProperty & { basePackage?: IPackage | null })[] = await Property.find(propertyFilterQuery)
      .lean()
      .sort({ updatedAt: -1 })
      .select('-createdAt -updatedAt -__v -deleted -owner -acceptBookingFrom -buildYear -status')
      .populate([
        { path: 'address.locationId', select: 'name code' },
        { path: 'serviceType', select: 'name' },
      ]);

    if (properties.length > 0) {
      // --- Build package filter ---
      const packageFilter: mongoose.FilterQuery<IPackage> = {
        propertyId: { $in: properties.map((p) => p._id) },
        active: true,
      };

      if (duration) packageFilter.duration = { $lte: parseInt(duration, 10) };
      if (noOfAdults) packageFilter.noOfAdults = parseInt(noOfAdults, 10);
      if (noOfChildren) packageFilter.noOfChildren = parseInt(noOfChildren, 10);

      let desiredDuration: number | null = null;
      if (startDateTime && endDateTime) {
        const start = new Date(startDateTime);
        const end = new Date(endDateTime);
        if (!isNaN(start.getTime()) && !isNaN(end.getTime())) {
          desiredDuration = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60));
          packageFilter.duration = { $lte: desiredDuration };
        }
      }

      // --- Fetch packages ---
      let packages = await Package.find(packageFilter)
        .populate([
          { path: 'unitTypeId', select: 'name totalUnits' },
          { path: 'amenities', select: 'name' },
          { path: 'taxes', select: 'name' },
        ])
        .lean();

      let responseMessage = 'Properties retrieved successfully';

      if (packages.length === 0 && startDateTime && endDateTime) {
        responseMessage = 'No packages match the desired time. Showing next available options.';

        const startDate = new Date(startDateTime);
        const endDate = new Date(endDateTime);

        if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
          desiredDuration = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60));

          // Fetch packages with durations closest to the desired duration
          packages = await Package.find({
            propertyId: { $in: properties.map((p) => p._id) },
            active: true,
          })
            .sort({ duration: 1 })
            .populate([
              { path: 'unitTypeId', select: 'name totalUnits' },
              { path: 'amenities', select: 'name' },
              { path: 'taxes', select: 'name' },
            ])
            .lean();

          // Filter packages with duration greater than desiredDuration (next available)
          packages = packages.filter((pkg) => pkg.duration > (desiredDuration ?? 0));

          if (packages.length === 0) {
            return ResponseUtil.success(res, 'No alternative packages available', []);
          }
        }
      }

      if (startDateTime && endDateTime) {
        const startDate = new Date(startDateTime);
        const endDate = new Date(endDateTime);

        if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
          // unique room types
          const uniqueRoomTypes = Array.from(new Set(packages.map((pkg) => pkg.unitTypeId as unknown as IUnitType)));

          // availability checks
          const availabilityResults = await Promise.all(
            properties.map((property) =>
              checkRoomAvailability({
                propertyId: property._id.toString(),
                unitTypes: uniqueRoomTypes,
                startDateTime,
                endDateTime,
              }),
            ),
          );

          const availabilityMap = new Map(availabilityResults.flat().map((r) => [r.unitTypeId, r.availableRooms]));

          // rate cards
          const packageIds = packages.map((pkg) => pkg._id.toString());
          const rateCardMap = await getRateCardPrices({ packageIds, startDateTime, endDateTime });

          // attach availability + price
          packages = packages
            .map((pkg) => {
              const rtId = (pkg.unitTypeId as unknown as IUnitType)._id.toString();
              const availableRooms = availabilityMap.get(rtId) || 0;
              const rateCardPrice = rateCardMap[pkg._id.toString()] ?? null;

              return {
                ...pkg,
                available: availableRooms,
                rateCardPrice,
              };
            })
            .filter((pkg) => pkg.available > 0);
        } else {
          packages = packages.map((pkg) => ({
            ...pkg,
            available: (pkg.unitTypeId as unknown as IUnitType).totalUnits,
            rateCardPrice: null,
          }));
        }
      }

      // --- Compute basePackage based on duration ---
      const propertyPackageMap = new Map<string, IPackage>();
      properties.forEach((property) => {
        const propertyPackages = packages.filter((pkg) => pkg.propertyId.toString() === property._id.toString());
        if (propertyPackages.length === 0) return;

        let selectedPackage: IPackage | null;
        if (desiredDuration !== null) {
          // Find exact duration match
          const exactMatches = propertyPackages.filter((pkg) => pkg.duration === desiredDuration);
          if (exactMatches.length > 0) {
            // Select the cheapest exact match
            selectedPackage = exactMatches.reduce((prev, curr) => {
              const prevPrice = (prev as unknown as { rateCardPrice: number }).rateCardPrice ?? prev.price;
              const currPrice = (curr as unknown as { rateCardPrice: number }).rateCardPrice ?? curr.price;
              return currPrice < prevPrice ? curr : prev;
            });
          } else {
            // Select the package with closest duration
            selectedPackage = propertyPackages.reduce((prev, curr) => {
              const prevDiff = Math.abs((prev.duration ?? 0) - desiredDuration);
              const currDiff = Math.abs((curr.duration ?? 0) - desiredDuration);
              if (currDiff < prevDiff) return curr;
              if (currDiff === prevDiff) {
                // Tiebreaker: lower price
                const prevPrice = (prev as unknown as { rateCardPrice: number }).rateCardPrice ?? prev.price;
                const currPrice = (curr as unknown as { rateCardPrice: number }).rateCardPrice ?? curr.price;
                return currPrice < prevPrice ? curr : prev;
              }
              return prev;
            });
          }
        } else {
          // Fallback to cheapest package if no duration specified
          selectedPackage = propertyPackages.reduce((prev, curr) => {
            const prevPrice = (prev as unknown as { rateCardPrice: number }).rateCardPrice ?? prev.price;
            const currPrice = (curr as unknown as { rateCardPrice: number }).rateCardPrice ?? curr.price;
            return currPrice < prevPrice ? curr : prev;
          });
        }

        if (selectedPackage) {
          propertyPackageMap.set(property._id.toString(), selectedPackage);
        }
      });

      // --- Attach basePackage ---
      properties.forEach((property) => {
        property.basePackage = propertyPackageMap.get(property._id.toString()) ?? null;
      });

      const filteredProperties: (IProperty & {
        basePackage?: IPackage | null;
        logo?: string;
        aboutProperty?: string;
        amenities?: mongoose.Types.ObjectId[];
        attachments?: string[];
      })[] = properties.filter(
        (p) =>
          p.basePackage &&
          ((p.basePackage as unknown as { rateCardPrice: number }).rateCardPrice ?? p.basePackage.price) > 0,
      );

      // get logo for all properties from property details
      const propertyIds = filteredProperties.map((p) => p._id.toString());
      const propertyDetails = await PropertyDetailsModel.find({ propertyId: { $in: propertyIds } })
        .select('propertyId businessLogo aboutProperty amenities attachments')
        .populate([
          {
            path: 'amenities',
            select: 'name',
          },
        ])
        .lean();
      const propertyLogoMap = new Map(propertyDetails.map((d) => [d.propertyId.toString(), d.businessLogo]));
      const propertyAboutMap = new Map(propertyDetails.map((d) => [d.propertyId.toString(), d.aboutProperty]));
      const amenitiesMap = new Map(propertyDetails.map((d) => [d.propertyId.toString(), d.amenities]));
      const propertiesAttachmentsMap = new Map(propertyDetails.map((d) => [d.propertyId.toString(), d.attachments]));
      filteredProperties.forEach((property) => {
        property.logo = propertyLogoMap.get(property._id.toString());
        property.aboutProperty = propertyAboutMap.get(property._id.toString());
        property.amenities = amenitiesMap.get(property._id.toString());
        property.attachments = propertiesAttachmentsMap.get(property._id.toString());
      });

      ResponseUtil.success(res, responseMessage, filteredProperties);
      return;
    }
    ResponseUtil.success(res, 'Properties retrieved successfully', properties);
  });

  get = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const property = await Property.findById(req.params.id).populate([
      {
        path: 'address.locationId',
      },
      {
        path: 'serviceType',
      },
      {
        path: 'owner',
        select: '-password -createdAt -updatedAt -__v -role',
      },
    ]);
    if (!property) {
      return ResponseUtil.notFound(res, 'Property not found');
    }
    ResponseUtil.success(res, 'Property retrieved successfully', property);
  });

  create = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const user = req.user;
    if (!user) {
      return ResponseUtil.unauthorized(res, 'Unauthorized');
    }
    if (user.role !== UserRoleEnum.MERCHANT && user.role !== UserRoleEnum.USER) {
      return ResponseUtil.forbidden(res, 'Merchant access required');
    }

    const property: IProperty = req.body;
    const existingProperty = await Property.findOne({
      name: property.name,
      serviceType: property.serviceType,
      'address.locationId': property.address.locationId,
    });
    if (existingProperty) {
      return ResponseUtil.conflict(res, 'Property already exists');
    }

    const newProperty = new Property({
      ...property,
      owner: user.userId,
      active: false,
      status: PropertyStatusEnum.PENDING,
    });

    await newProperty.save();

    await newProperty.populate([
      {
        path: 'owner',
        select: '-password -createdAt -updatedAt -__v -role',
      },
    ]);

    const poc = newProperty.owner as unknown as IUser;

    await this.emailService.sendApprovalStatusEmail(poc.email, poc.firstName, property, 'Pending');
    ResponseUtil.created(res, 'Property created successfully', newProperty);
  });

  update = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const property = await Property.findById(req.params.id);
    if (!property) {
      return ResponseUtil.notFound(res, 'Property not found');
    }

    const existingProperty = await Property.findOne({
      name: req.body.name,
      _id: { $ne: req.params.id },
    });

    if (existingProperty) {
      return ResponseUtil.conflict(res, 'Property name already exists');
    }

    const updatedProperty = await Property.findByIdAndUpdate(req.params.id, req.body, { new: true });

    ResponseUtil.success(res, 'Property updated successfully', updatedProperty);
  });

  updateStatus = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { status } = req.body;
    const propertyId = req.params.id;

    const property = await Property.findById(propertyId);

    if (!property) {
      return ResponseUtil.notFound(res, 'Property not found');
    }

    if (status === PropertyStatusEnum.APPROVED && property.status === PropertyStatusEnum.APPROVED && property.active) {
      return ResponseUtil.badRequest(res, 'Property already approved and active');
    }

    const { hasUnitTypes, hasPropertyDetails, hasBusinessDetails, hasPropertyPolicies, hasAmenities } =
      await this.getPropertyDataCompleteness(propertyId);

    if (
      status === PropertyStatusEnum.APPROVED &&
      !(hasUnitTypes && hasPropertyDetails && hasBusinessDetails && hasPropertyPolicies && hasAmenities)
    ) {
      return ResponseUtil.badRequest(res, 'Property is not complete');
    }

    const updatedProperty = await Property.findByIdAndUpdate(req.params.id, req.body, { new: true });

    ResponseUtil.success(res, 'Property status updated successfully', updatedProperty);
  });

  delete = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const property = await Property.findByIdAndDelete(req.params.id);

    if (!property) {
      return ResponseUtil.notFound(res, 'Property not found');
    }

    ResponseUtil.success(res, 'Property deleted successfully', null, 204);
  });

  patchStatus = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { status, active } = req.body;

    // Get the current property to compare status changes
    const currentProperty = await Property.findById(req.params.id);
    if (!currentProperty) {
      return ResponseUtil.notFound(res, 'Property not found');
    }

    const property = await Property.findByIdAndUpdate(req.params.id, { status, active }, { new: true }).populate([
      {
        path: 'owner',
      },
    ]);

    if (!property) {
      return ResponseUtil.notFound(res, 'Property not found');
    }

    // Send email notifications for status changes
    const poc = property.owner as unknown as IUser;
    try {
      // Check if approval status changed
      if (status && status !== currentProperty.status && (status === 'Approved' || status === 'Rejected')) {
        void this.emailService.sendApprovalStatusEmail(poc.email, poc.firstName, property, status);
      }

      // Check if active status changed
      if (active !== undefined && active !== currentProperty.active) {
        void this.emailService.sendActiveStatusEmail(poc.email, poc.firstName, property, active);
      }
    } catch (emailError) {
      loggerService.error(`Failed to send email`, 'property.controller.ts', emailError);
      // Don't fail the request if email fails
    }

    ResponseUtil.success(res, 'Property status updated successfully', property);
  });

  hasData = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const property = await Property.findById(req.params.id);
    if (!property) {
      return ResponseUtil.notFound(res, 'Property not found');
    }

    const hasData = await this.getPropertyDataCompleteness(req.params.id);

    ResponseUtil.success(res, 'Property data check successful', hasData);
  });

  private async getPropertyDataCompleteness(propertyId: string) {
    const [unitTypesCount, propertyDetails, businessDetailsCount, propertyPoliciesCount] = await Promise.all([
      UnitTypeModel.countDocuments({ propertyId }),
      PropertyDetailsModel.findOne({ propertyId }).select('amenities').lean(),
      BusinessDetailsModel.countDocuments({ propertyId }),
      PropertyPoliciesModel.countDocuments({ propertyId }),
    ]);

    const hasUnitTypes = unitTypesCount > 0;
    const hasPropertyDetails = !!propertyDetails;
    const hasBusinessDetails = businessDetailsCount > 0;
    const hasPropertyPolicies = propertyPoliciesCount > 0;
    const hasAmenities = !!(
      propertyDetails &&
      Array.isArray(propertyDetails.amenities) &&
      propertyDetails.amenities.length > 0
    );

    return {
      hasUnitTypes,
      hasPropertyDetails,
      hasBusinessDetails,
      hasPropertyPolicies,
      hasAmenities,
    };
  }
}
