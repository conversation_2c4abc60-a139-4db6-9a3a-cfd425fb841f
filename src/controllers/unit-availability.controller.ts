import { IRequest, IResponse, IUnitAvailability } from '../types';
import UnitAvailabilityModel from '../models/unit-availability.model';
import UnitTypeModel from '../models/unit-type.model';
import { ResponseUtil } from '../utils/response';
import { asyncRequestHandler } from '../middlewares/async-request-handler';

export class UnitAvailabilityController {
  private static Instance: UnitAvailabilityController;

  public static getInstance(): UnitAvailabilityController {
    if (!UnitAvailabilityController.Instance) {
      UnitAvailabilityController.Instance = new UnitAvailabilityController();
    }
    return UnitAvailabilityController.Instance;
  }

  private constructor() {}

  create = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const unitAvailabilities = req.body;

    const unitTypes = await UnitTypeModel.find({
      _id: {
        $in: unitAvailabilities.map((ra: IUnitAvailability) => ra.unitTypeId),
      },
    });

    // Collect validation errors
    const validationErrors: string[] = [];
    unitAvailabilities.forEach((ra: IUnitAvailability) => {
      const unitType = unitTypes.find((rt) => rt._id.toString() === ra.unitTypeId.toString());
      if (!unitType) {
        validationErrors.push(`Unit type ${ra.unitTypeId} not found`);
      } else if (unitType.totalUnits < ra.availability) {
        validationErrors.push(`Availability cannot be greater than no of rooms for unit type ${unitType.name}`);
      }
    });

    // If there are any validation errors, return them all at once
    if (validationErrors.length > 0) {
      return ResponseUtil.badRequest(res, 'Validation errors', validationErrors.join(', '));
    }

    // Prepare bulk operations with upsert
    const bulkOperations = unitAvailabilities.map((unitAvailability: IUnitAvailability) => ({
      updateOne: {
        filter: {
          unitTypeId: unitAvailability.unitTypeId,
          dateTime: unitAvailability.dateTime,
          propertyId: req.params.propertyId,
        },
        update: { $set: unitAvailability },
        upsert: true,
      },
    }));

    const result = await UnitAvailabilityModel.bulkWrite(bulkOperations);

    ResponseUtil.success(res, 'Unit availability created/updated successfully', result);
  });

  getByDate = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { date } = req.query;
    const { propertyId } = req.params;

    const startDate = new Date(date as string);
    startDate.setHours(0, 0, 0, 0);

    const endDate = new Date(date as string);
    endDate.setHours(23, 59, 59, 999);

    if (!date) {
      return ResponseUtil.badRequest(res, 'Please provide date');
    }

    const query = {
      propertyId: propertyId,
      dateTime: {
        $gte: startDate,
        $lte: endDate,
      },
    };

    const unitAvailabilities = await UnitAvailabilityModel.find(query).populate([
      {
        path: 'unitTypeId',
        select: 'name',
      },
    ]);

    ResponseUtil.success(res, 'Unit availabilities retrieved successfully', unitAvailabilities);
  });
}
