import { IRequest, IResponse, IUnitAvailability, PaymentStatusEnum, ReservationStatusEnum } from '../types';
import UnitAvailabilityModel from '../models/unit-availability.model';
import UnitTypeModel from '../models/unit-type.model';
import ReservationItemModel from '../models/reservation-item.model';
import { ResponseUtil } from '../utils/response';
import { asyncRequestHandler } from '../middlewares/async-request-handler';
import mongoose from 'mongoose';

export class UnitAvailabilityController {
  private static Instance: UnitAvailabilityController;

  public static getInstance(): UnitAvailabilityController {
    if (!UnitAvailabilityController.Instance) {
      UnitAvailabilityController.Instance = new UnitAvailabilityController();
    }
    return UnitAvailabilityController.Instance;
  }

  private constructor() {}

  create = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { unitTypeId, fromDate, toDate, days, fromTime, toTime, quantityAvailable, reason } = req.body;
    const { propertyId } = req.params;

    if (!unitTypeId || !fromDate || !toDate || !days || quantityAvailable === undefined) {
      return ResponseUtil.badRequest(
        res,
        'Missing required fields: unitTypeId, fromDate, toDate, days, quantityAvailable',
      );
    }

    try {
      // Validate unit type exists
      const unitType = await UnitTypeModel.findById(unitTypeId);
      if (!unitType) {
        return ResponseUtil.badRequest(res, 'Unit type not found');
      }

      // Validate quantity doesn't exceed total units
      if (quantityAvailable > unitType.totalUnits) {
        return ResponseUtil.badRequest(
          res,
          `Quantity available (${quantityAvailable}) cannot exceed total units (${unitType.totalUnits})`,
        );
      }

      // Handle overlapping rules by archiving them
      await UnitAvailabilityModel.updateMany(
        {
          unitTypeId,
          isActive: true,
          fromDate: { $lte: new Date(toDate) },
          toDate: { $gte: new Date(fromDate) },
          $or: [
            { days: { $in: days.map((day: string) => day.toLowerCase()) } },
            { days: 'all' },
            { $expr: { $in: ['all', days.map((day: string) => day.toLowerCase())] } },
          ],
        },
        {
          $set: {
            isActive: false,
            archivedAt: new Date(),
            archivedReason: 'Overridden by new rule',
          },
        },
      );

      // Create the new rule
      const newRule = new UnitAvailabilityModel({
        propertyId,
        unitTypeId,
        fromDate: new Date(fromDate),
        toDate: new Date(toDate),
        days: days.map((day: string) => day.toLowerCase()),
        fromTime: fromTime || '00:00',
        toTime: toTime || '23:59',
        quantityAvailable,
        reason,
        isActive: true,
        version: 1,
      });

      const savedRule = await newRule.save();
      ResponseUtil.success(res, 'Availability rule created successfully', savedRule);
    } catch (error) {
      ResponseUtil.badRequest(res, error instanceof Error ? error.message : 'Failed to create availability rule');
    }
  });

  getAll = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { propertyId } = req.params;

    try {
      const availabilityRules = await UnitAvailabilityModel.find({
        propertyId,
        isActive: true,
      })
        .populate('unitTypeId', 'name code totalUnits')
        .sort({ fromDate: 1 });

      ResponseUtil.success(res, 'Availability rules retrieved successfully', availabilityRules);
    } catch (error) {
      ResponseUtil.badRequest(res, error instanceof Error ? error.message : 'Failed to retrieve availability rules');
    }
  });

  getOne = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { ruleId } = req.params;

    try {
      const availabilityRule = await UnitAvailabilityModel.findOne({
        _id: ruleId,
        isActive: true,
      }).populate('unitTypeId', 'name code totalUnits');

      if (!availabilityRule) {
        return ResponseUtil.badRequest(res, 'Availability rule not found');
      }

      ResponseUtil.success(res, 'Availability rule retrieved successfully', availabilityRule);
    } catch (error) {
      ResponseUtil.badRequest(res, error instanceof Error ? error.message : 'Failed to retrieve availability rule');
    }
  });

  updateOne = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { ruleId } = req.params;
    const { unitTypeId, fromDate, toDate, days, fromTime, toTime, quantityAvailable, reason } = req.body;

    try {
      const availabilityRule = await UnitAvailabilityModel.findOne({
        _id: ruleId,
        isActive: true,
      });

      if (!availabilityRule) {
        return ResponseUtil.badRequest(res, 'Availability rule not found');
      }

      // Validate unit type exists if unitTypeId is being updated
      if (unitTypeId && unitTypeId !== availabilityRule.unitTypeId?.toString()) {
        const unitType = await UnitTypeModel.findById(unitTypeId);
        if (!unitType) {
          return ResponseUtil.badRequest(res, 'Unit type not found');
        }

        // Validate quantity doesn't exceed total units
        const newQuantity = quantityAvailable !== undefined ? quantityAvailable : availabilityRule.quantityAvailable;
        if (newQuantity && newQuantity > unitType.totalUnits) {
          return ResponseUtil.badRequest(
            res,
            `Quantity available (${newQuantity}) cannot exceed total units (${unitType.totalUnits})`,
          );
        }
      }

      // Update fields
      if (unitTypeId) availabilityRule.unitTypeId = unitTypeId;
      if (fromDate) availabilityRule.fromDate = new Date(fromDate);
      if (toDate) availabilityRule.toDate = new Date(toDate);
      if (days) availabilityRule.days = days.map((day: string) => day.toLowerCase());
      if (fromTime) availabilityRule.fromTime = fromTime;
      if (toTime) availabilityRule.toTime = toTime;
      if (quantityAvailable !== undefined) availabilityRule.quantityAvailable = quantityAvailable;
      if (reason !== undefined) availabilityRule.reason = reason;

      const updatedRule = await availabilityRule.save();
      ResponseUtil.success(res, 'Availability rule updated successfully', updatedRule);
    } catch (error) {
      ResponseUtil.badRequest(res, error instanceof Error ? error.message : 'Failed to update availability rule');
    }
  });

  getAvailabilityByDate = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { date, unitTypeId } = req.query;
    const { propertyId } = req.params;

    if (!date) {
      return ResponseUtil.badRequest(res, 'Please provide date parameter');
    }

    try {
      const queryDate = new Date(date as string);
      const dayName = queryDate.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();

      const query: mongoose.FilterQuery<IUnitAvailability> = {
        propertyId,
        isActive: true,
        fromDate: { $lte: queryDate },
        toDate: { $gte: queryDate },
        $or: [{ days: 'all' }, { days: dayName }],
      };

      // Filter by unitTypeId if provided
      if (unitTypeId) {
        query.unitTypeId = unitTypeId;
      }

      const availabilityRules = await UnitAvailabilityModel.find(query)
        .populate('unitTypeId', 'name code totalUnits')
        .sort({ createdAt: -1 });

      // Calculate actual availability by considering confirmed bookings
      const availabilityWithBookings = await Promise.all(
        availabilityRules.map(async (rule) => {
          const startOfDay = new Date(queryDate);
          startOfDay.setHours(0, 0, 0, 0);
          const endOfDay = new Date(queryDate);
          endOfDay.setHours(23, 59, 59, 999);

          const confirmedBookings = await ReservationItemModel.countDocuments({
            unitTypeId: rule.unitTypeId,
            status: { $in: [ReservationStatusEnum.CONFIRMED, ReservationStatusEnum.CONFIRMED] },
            paymentStatus: { $ne: PaymentStatusEnum.FAILED },
            startDateTime: { $lt: endOfDay },
            endDateTime: { $gt: startOfDay },
          });

          const ruleQuantity = rule.quantityAvailable || 0;
          const availableUnits = Math.max(0, ruleQuantity - confirmedBookings);

          return {
            ...rule.toObject(),
            confirmedBookings,
            availableUnits,
            isBlocked: availableUnits === 0,
            blockReason: availableUnits === 0 ? rule.reason : undefined,
          };
        }),
      );

      ResponseUtil.success(res, 'Availability retrieved successfully', availabilityWithBookings);
    } catch (error) {
      ResponseUtil.badRequest(res, error instanceof Error ? error.message : 'Failed to retrieve availability');
    }
  });
}
