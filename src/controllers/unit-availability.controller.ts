import { IRequest, IResponse, IUnitAvailability, PaymentStatusEnum, ReservationStatusEnum } from '../types';
import UnitAvailabilityModel from '../models/unit-availability.model';
import UnitTypeModel from '../models/unit-type.model';
import ReservationItemModel from '../models/reservation-item.model';
import { ResponseUtil } from '../utils/response';
import { asyncRequestHandler } from '../middlewares/async-request-handler';
import mongoose from 'mongoose';

export class UnitAvailabilityController {
  private static Instance: UnitAvailabilityController;

  public static getInstance(): UnitAvailabilityController {
    if (!UnitAvailabilityController.Instance) {
      UnitAvailabilityController.Instance = new UnitAvailabilityController();
    }
    return UnitAvailabilityController.Instance;
  }

  private constructor() {}

  // Legacy create method (keeping for backward compatibility)
  create = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const unitAvailabilities = req.body;

    const unitTypes = await UnitTypeModel.find({
      _id: {
        $in: unitAvailabilities.map((ra: IUnitAvailability) => ra.unitTypeId),
      },
    });

    // Collect validation errors
    const validationErrors: string[] = [];
    unitAvailabilities.forEach((ra: IUnitAvailability) => {
      const unitType = unitTypes.find((rt) => rt._id.toString() === ra.unitTypeId.toString());
      if (!unitType) {
        validationErrors.push(`Unit type ${ra.unitTypeId} not found`);
      } else if (ra.availability && unitType.totalUnits < ra.availability) {
        validationErrors.push(`Availability cannot be greater than no of rooms for unit type ${unitType.name}`);
      }
    });

    // If there are any validation errors, return them all at once
    if (validationErrors.length > 0) {
      return ResponseUtil.badRequest(res, 'Validation errors', validationErrors.join(', '));
    }

    // Prepare bulk operations with upsert
    const bulkOperations = unitAvailabilities.map((unitAvailability: IUnitAvailability) => ({
      updateOne: {
        filter: {
          unitTypeId: unitAvailability.unitTypeId,
          dateTime: unitAvailability.dateTime,
          propertyId: req.params.propertyId,
        },
        update: { $set: unitAvailability },
        upsert: true,
      },
    }));

    const result = await UnitAvailabilityModel.bulkWrite(bulkOperations);

    ResponseUtil.success(res, 'Unit availability created/updated successfully', result);
  });

  // Legacy getByDate method (keeping for backward compatibility)
  getByDate = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { date } = req.query;
    const { propertyId } = req.params;

    const startDate = new Date(date as string);
    startDate.setHours(0, 0, 0, 0);

    const endDate = new Date(date as string);
    endDate.setHours(23, 59, 59, 999);

    if (!date) {
      return ResponseUtil.badRequest(res, 'Please provide date');
    }

    const query = {
      propertyId: propertyId,
      dateTime: {
        $gte: startDate,
        $lte: endDate,
      },
    };

    const unitAvailabilities = await UnitAvailabilityModel.find(query).populate([
      {
        path: 'unitTypeId',
        select: 'name',
      },
    ]);

    ResponseUtil.success(res, 'Unit availabilities retrieved successfully', unitAvailabilities);
  });

  // New rule-based endpoints

  /**
   * Create a new availability rule
   */
  createRule = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { unitTypeId, fromDate, toDate, days, fromTime, toTime, quantityAvailable, reason } = req.body;
    const { propertyId } = req.params;

    if (!unitTypeId || !fromDate || !toDate || !days || quantityAvailable === undefined) {
      return ResponseUtil.badRequest(res, 'Missing required fields: unitTypeId, fromDate, toDate, days, quantityAvailable');
    }

    try {
      const result = await this.createAvailabilityRule({
        propertyId,
        unitTypeId,
        fromDate: new Date(fromDate),
        toDate: new Date(toDate),
        days,
        fromTime: fromTime || '00:00',
        toTime: toTime || '23:59',
        quantityAvailable,
        reason
      });

      ResponseUtil.success(res, 'Availability rule created successfully', result);
    } catch (error) {
      ResponseUtil.badRequest(res, error instanceof Error ? error.message : 'Failed to create availability rule');
    }
  });

  /**
   * Create a quick block rule
   */
  createQuickBlock = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { unitTypeId, date, fromTime, toTime, reason } = req.body;
    const { propertyId } = req.params;

    if (!unitTypeId || !date || !reason) {
      return ResponseUtil.badRequest(res, 'Missing required fields: unitTypeId, date, reason');
    }

    try {
      const result = await this.createAvailabilityRule({
        propertyId,
        unitTypeId,
        fromDate: new Date(date),
        toDate: new Date(date),
        days: ['all'],
        fromTime: fromTime || '00:00',
        toTime: toTime || '23:59',
        quantityAvailable: 0,
        reason
      });

      ResponseUtil.success(res, 'Quick block created successfully', result);
    } catch (error) {
      ResponseUtil.badRequest(res, error instanceof Error ? error.message : 'Failed to create quick block');
    }
  });

  /**
   * Resolve availability for a booking query
   */
  resolveAvailability = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { unitTypeId, startDateTime, endDateTime } = req.query;

    if (!unitTypeId || !startDateTime || !endDateTime) {
      return ResponseUtil.badRequest(res, 'Missing required query parameters: unitTypeId, startDateTime, endDateTime');
    }

    try {
      const result = await this.resolveAvailabilityForBooking(
        unitTypeId as string,
        new Date(startDateTime as string),
        new Date(endDateTime as string)
      );

      ResponseUtil.success(res, 'Availability resolved successfully', result);
    } catch (error) {
      ResponseUtil.badRequest(res, error instanceof Error ? error.message : 'Failed to resolve availability');
    }
  });

  /**
   * Get all active rules for a unit type
   */
  getRulesForUnitType = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { unitTypeId } = req.params;

    if (!unitTypeId) {
      return ResponseUtil.badRequest(res, 'Unit type ID is required');
    }

    try {
      const rules = await UnitAvailabilityModel.find({
        unitTypeId,
        isActive: true
      }).sort({ fromDate: 1 });

      ResponseUtil.success(res, 'Availability rules retrieved successfully', rules);
    } catch (error) {
      ResponseUtil.badRequest(res, error instanceof Error ? error.message : 'Failed to retrieve availability rules');
    }
  });

  /**
   * Archive an availability rule
   */
  archiveRule = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { ruleId } = req.params;
    const { reason } = req.body;

    if (!ruleId) {
      return ResponseUtil.badRequest(res, 'Rule ID is required');
    }

    try {
      const rule = await UnitAvailabilityModel.findById(ruleId);
      if (!rule) {
        return ResponseUtil.badRequest(res, 'Rule not found');
      }

      rule.isActive = false;
      rule.archivedAt = new Date();
      rule.archivedReason = reason || 'Manually archived';

      const archivedRule = await rule.save();
      ResponseUtil.success(res, 'Availability rule archived successfully', archivedRule);
    } catch (error) {
      ResponseUtil.badRequest(res, error instanceof Error ? error.message : 'Failed to archive availability rule');
    }
  });

  // Helper methods for rule management

  /**
   * Create an availability rule with overlap detection and resolution
   */
  private async createAvailabilityRule(ruleInput: {
    propertyId: string;
    unitTypeId: string;
    fromDate: Date;
    toDate: Date;
    days: string[];
    fromTime: string;
    toTime: string;
    quantityAvailable: number;
    reason?: string;
  }) {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Validate unit type exists
      const unitType = await UnitTypeModel.findById(ruleInput.unitTypeId).session(session);
      if (!unitType) {
        throw new Error('Unit type not found');
      }

      // Validate quantity doesn't exceed total units
      if (ruleInput.quantityAvailable > unitType.totalUnits) {
        throw new Error(`Quantity available (${ruleInput.quantityAvailable}) cannot exceed total units (${unitType.totalUnits})`);
      }

      // Find overlapping rules
      const overlappingRules = await this.findOverlappingAvailabilityRules(ruleInput, session);

      // Create the new rule
      const newRule = new UnitAvailabilityModel({
        propertyId: ruleInput.propertyId,
        unitTypeId: ruleInput.unitTypeId,
        fromDate: ruleInput.fromDate,
        toDate: ruleInput.toDate,
        days: ruleInput.days.map(day => day.toLowerCase()),
        fromTime: ruleInput.fromTime,
        toTime: ruleInput.toTime,
        quantityAvailable: ruleInput.quantityAvailable,
        reason: ruleInput.reason,
        isActive: true,
        version: 1
      });

      // Handle overlaps by merging with minimum quantity
      for (const existingRule of overlappingRules) {
        const minQuantity = Math.min(existingRule.quantityAvailable || 0, ruleInput.quantityAvailable);

        // Archive the existing rule
        existingRule.isActive = false;
        existingRule.archivedAt = new Date();
        existingRule.archivedReason = 'Merged with new rule (minimum quantity applied)';
        await existingRule.save({ session });

        // Update new rule with minimum quantity for overlapping period
        if (minQuantity === 0) {
          newRule.reason = newRule.reason || existingRule.reason || 'Blocked due to rule merge';
        }
        newRule.quantityAvailable = minQuantity;

        if (this.isExactAvailabilityMatch(existingRule, newRule)) {
          newRule.version = (existingRule.version || 1) + 1;
        }
      }

      await newRule.save({ session });
      await session.commitTransaction();

      return {
        action: overlappingRules.length > 0 ? 'merge' : 'create',
        affectedRules: overlappingRules,
        newRule
      };
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Find overlapping availability rules for a given rule input
   */
  private async findOverlappingAvailabilityRules(
    ruleInput: any,
    session?: mongoose.ClientSession
  ): Promise<IUnitAvailability[]> {
    const query = {
      unitTypeId: ruleInput.unitTypeId,
      isActive: true,
      fromDate: { $lte: ruleInput.toDate },
      toDate: { $gte: ruleInput.fromDate },
      $or: [
        { days: { $in: ruleInput.days.map((day: string) => day.toLowerCase()) } },
        { days: 'all' },
        { $expr: { $in: ['all', ruleInput.days.map((day: string) => day.toLowerCase())] } }
      ]
    };

    const options = session ? { session } : {};
    return await UnitAvailabilityModel.find(query, null, options);
  }

  /**
   * Check if two availability rules are an exact match
   */
  private isExactAvailabilityMatch(rule1: IUnitAvailability, rule2: IUnitAvailability): boolean {
    return (
      rule1.fromDate?.getTime() === rule2.fromDate?.getTime() &&
      rule1.toDate?.getTime() === rule2.toDate?.getTime() &&
      rule1.fromTime === rule2.fromTime &&
      rule1.toTime === rule2.toTime &&
      JSON.stringify(rule1.days?.sort()) === JSON.stringify(rule2.days?.sort())
    );
  }

  /**
   * Resolve availability for a booking query
   */
  private async resolveAvailabilityForBooking(
    unitTypeId: string,
    startDateTime: Date,
    endDateTime: Date
  ) {
    // Get unit type for fallback capacity
    const unitType = await UnitTypeModel.findById(unitTypeId);
    if (!unitType) {
      throw new Error('Unit type not found');
    }

    const hourlyBreakdown: Array<{
      hour: Date;
      totalCapacity: number;
      ruleQuantity?: number;
      confirmedBookings: number;
      availableUnits: number;
      blockReason?: string;
    }> = [];
    let minAvailability = unitType.totalUnits;
    let blockReason: string | undefined;

    // Generate hourly slots
    const currentHour = new Date(startDateTime);
    currentHour.setMinutes(0, 0, 0); // Round to hour

    while (currentHour < endDateTime) {
      const nextHour = new Date(currentHour);
      nextHour.setHours(currentHour.getHours() + 1);

      // Find applicable rule for this hour
      const applicableRule = await this.findApplicableAvailabilityRule(unitTypeId, currentHour);

      // Get confirmed bookings for this hour
      const confirmedBookings = await this.getConfirmedBookingsCount(unitTypeId, currentHour, nextHour);

      // Calculate availability
      const ruleQuantity = applicableRule?.quantityAvailable ?? unitType.totalUnits;
      const availableUnits = Math.max(0, ruleQuantity - confirmedBookings);

      hourlyBreakdown.push({
        hour: new Date(currentHour),
        totalCapacity: unitType.totalUnits,
        ruleQuantity: applicableRule?.quantityAvailable,
        confirmedBookings,
        availableUnits,
        blockReason: applicableRule?.reason
      });

      // Track minimum availability and block reason
      if (availableUnits < minAvailability) {
        minAvailability = availableUnits;
        if (availableUnits === 0 && applicableRule?.reason) {
          blockReason = applicableRule.reason;
        }
      }

      currentHour.setHours(currentHour.getHours() + 1);
    }

    return {
      available: minAvailability,
      reason: blockReason,
      hourlyBreakdown
    };
  }

  /**
   * Find the most applicable availability rule for a specific date and time
   */
  private async findApplicableAvailabilityRule(unitTypeId: string, dateTime: Date): Promise<IUnitAvailability | null> {
    const dayName = dateTime.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
    const timeString = dateTime.toTimeString().substring(0, 5); // HH:MM format

    const rules = await UnitAvailabilityModel.find({
      unitTypeId,
      isActive: true,
      fromDate: { $lte: dateTime },
      toDate: { $gte: dateTime },
      $or: [
        { days: 'all' },
        { days: dayName }
      ],
      fromTime: { $lte: timeString },
      toTime: { $gte: timeString }
    }).sort({ createdAt: -1 }); // Most recent first

    return rules.length > 0 ? rules[0] : null;
  }

  /**
   * Get count of confirmed bookings for a specific time period
   */
  private async getConfirmedBookingsCount(
    unitTypeId: string,
    startTime: Date,
    endTime: Date
  ): Promise<number> {
    const confirmedBookings = await ReservationItemModel.countDocuments({
      unitTypeId,
      status: { $in: [ReservationStatusEnum.CONFIRMED, ReservationStatusEnum.CHECKED_IN] },
      paymentStatus: { $ne: PaymentStatusEnum.FAILED },
      startDateTime: { $lt: endTime },
      endDateTime: { $gt: startTime }
    });

    return confirmedBookings;
  }
}
