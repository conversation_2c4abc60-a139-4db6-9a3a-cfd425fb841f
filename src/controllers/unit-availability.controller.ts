import { IRequest, IResponse, IUnitAvailability } from '../types';
import UnitAvailabilityModel from '../models/unit-availability.model';
import UnitTypeModel from '../models/unit-type.model';
import { ResponseUtil } from '../utils/response';
import { asyncRequestHandler } from '../middlewares/async-request-handler';
import { AvailabilityRuleService } from '../services/availability-rule.service';

export class UnitAvailabilityController {
  private static Instance: UnitAvailabilityController;
  private availabilityRuleService: AvailabilityRuleService;

  public static getInstance(): UnitAvailabilityController {
    if (!UnitAvailabilityController.Instance) {
      UnitAvailabilityController.Instance = new UnitAvailabilityController();
    }
    return UnitAvailabilityController.Instance;
  }

  private constructor() {
    this.availabilityRuleService = AvailabilityRuleService.getInstance();
  }

  create = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const unitAvailabilities = req.body;

    const unitTypes = await UnitTypeModel.find({
      _id: {
        $in: unitAvailabilities.map((ra: IUnitAvailability) => ra.unitTypeId),
      },
    });

    // Collect validation errors
    const validationErrors: string[] = [];
    unitAvailabilities.forEach((ra: IUnitAvailability) => {
      const unitType = unitTypes.find((rt) => rt._id.toString() === ra.unitTypeId.toString());
      if (!unitType) {
        validationErrors.push(`Unit type ${ra.unitTypeId} not found`);
      } else if (unitType.totalUnits < ra.availability) {
        validationErrors.push(`Availability cannot be greater than no of rooms for unit type ${unitType.name}`);
      }
    });

    // If there are any validation errors, return them all at once
    if (validationErrors.length > 0) {
      return ResponseUtil.badRequest(res, 'Validation errors', validationErrors.join(', '));
    }

    // Prepare bulk operations with upsert
    const bulkOperations = unitAvailabilities.map((unitAvailability: IUnitAvailability) => ({
      updateOne: {
        filter: {
          unitTypeId: unitAvailability.unitTypeId,
          dateTime: unitAvailability.dateTime,
          propertyId: req.params.propertyId,
        },
        update: { $set: unitAvailability },
        upsert: true,
      },
    }));

    const result = await UnitAvailabilityModel.bulkWrite(bulkOperations);

    ResponseUtil.success(res, 'Unit availability created/updated successfully', result);
  });

  getByDate = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { date } = req.query;
    const { propertyId } = req.params;

    const startDate = new Date(date as string);
    startDate.setHours(0, 0, 0, 0);

    const endDate = new Date(date as string);
    endDate.setHours(23, 59, 59, 999);

    if (!date) {
      return ResponseUtil.badRequest(res, 'Please provide date');
    }

    const query = {
      propertyId: propertyId,
      dateTime: {
        $gte: startDate,
        $lte: endDate,
      },
    };

    const unitAvailabilities = await UnitAvailabilityModel.find(query).populate([
      {
        path: 'unitTypeId',
        select: 'name',
      },
    ]);

    ResponseUtil.success(res, 'Unit availabilities retrieved successfully', unitAvailabilities);
  });

  // New rule-based endpoints

  /**
   * Create a new availability rule
   */
  createRule = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { unitTypeId, fromDate, toDate, days, fromTime, toTime, quantityAvailable, reason } = req.body;

    if (!unitTypeId || !fromDate || !toDate || !days || quantityAvailable === undefined) {
      return ResponseUtil.badRequest(res, 'Missing required fields: unitTypeId, fromDate, toDate, days, quantityAvailable');
    }

    try {
      const result = await this.availabilityRuleService.createRule({
        unitTypeId,
        fromDate: new Date(fromDate),
        toDate: new Date(toDate),
        days,
        fromTime,
        toTime,
        quantityAvailable,
        reason
      });

      ResponseUtil.success(res, 'Availability rule created successfully', result);
    } catch (error) {
      ResponseUtil.badRequest(res, error instanceof Error ? error.message : 'Failed to create availability rule');
    }
  });

  /**
   * Create a quick block rule
   */
  createQuickBlock = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { unitTypeId, date, fromTime, toTime, reason } = req.body;

    if (!unitTypeId || !date || !reason) {
      return ResponseUtil.badRequest(res, 'Missing required fields: unitTypeId, date, reason');
    }

    try {
      const result = await this.availabilityRuleService.createQuickBlock(
        unitTypeId,
        new Date(date),
        fromTime,
        toTime,
        reason
      );

      ResponseUtil.success(res, 'Quick block created successfully', result);
    } catch (error) {
      ResponseUtil.badRequest(res, error instanceof Error ? error.message : 'Failed to create quick block');
    }
  });

  /**
   * Resolve availability for a booking query
   */
  resolveAvailability = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { unitTypeId, startDateTime, endDateTime } = req.query;

    if (!unitTypeId || !startDateTime || !endDateTime) {
      return ResponseUtil.badRequest(res, 'Missing required query parameters: unitTypeId, startDateTime, endDateTime');
    }

    try {
      const result = await this.availabilityRuleService.resolveAvailability(
        unitTypeId as string,
        new Date(startDateTime as string),
        new Date(endDateTime as string)
      );

      ResponseUtil.success(res, 'Availability resolved successfully', result);
    } catch (error) {
      ResponseUtil.badRequest(res, error instanceof Error ? error.message : 'Failed to resolve availability');
    }
  });

  /**
   * Get all active rules for a unit type
   */
  getRulesForUnitType = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { unitTypeId } = req.params;

    if (!unitTypeId) {
      return ResponseUtil.badRequest(res, 'Unit type ID is required');
    }

    try {
      const rules = await this.availabilityRuleService.getRulesForUnitType(unitTypeId);
      ResponseUtil.success(res, 'Availability rules retrieved successfully', rules);
    } catch (error) {
      ResponseUtil.badRequest(res, error instanceof Error ? error.message : 'Failed to retrieve availability rules');
    }
  });

  /**
   * Archive an availability rule
   */
  archiveRule = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { ruleId } = req.params;
    const { reason } = req.body;

    if (!ruleId) {
      return ResponseUtil.badRequest(res, 'Rule ID is required');
    }

    try {
      const archivedRule = await this.availabilityRuleService.archiveRule(ruleId, reason);
      ResponseUtil.success(res, 'Availability rule archived successfully', archivedRule);
    } catch (error) {
      ResponseUtil.badRequest(res, error instanceof Error ? error.message : 'Failed to archive availability rule');
    }
  });
}
