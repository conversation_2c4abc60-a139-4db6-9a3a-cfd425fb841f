import { IRateCard, IRequest, IResponse } from '../types';
import RateCard from '../models/rate-card.model';
import { ResponseUtil } from '../utils/response';
import { asyncRequestHandler } from '../middlewares/async-request-handler';

export class RateCardController {
  private static Instance: RateCardController;

  public static getInstance(): RateCardController {
    if (!RateCardController.Instance) {
      RateCardController.Instance = new RateCardController();
    }
    return RateCardController.Instance;
  }

  private constructor() {}

  create = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const rateCards = req.body.rateCards;

    // Prepare bulk operations with upsert
    const bulkOperations = rateCards.map((rateCard: IRateCard) => ({
      updateOne: {
        filter: {
          packageId: rateCard.packageId,
          dateTime: rateCard.dateTime,
          propertyId: req.params.propertyId,
        },
        update: { $set: rateCard },
        upsert: true,
      },
    }));

    const result = await RateCard.bulkWrite(bulkOperations);

    ResponseUtil.success(res, 'Rate cards created/updated successfully', result);
  });

  getByDate = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { date } = req.query;
    const { propertyId } = req.params;

    if (!date) {
      return ResponseUtil.badRequest(res, 'Please provide date parameter');
    }

    const startDate = new Date(date as string);
    startDate.setHours(0, 0, 0, 0);

    const endDate = new Date(date as string);
    endDate.setHours(23, 59, 59, 999);

    const query = {
      propertyId: propertyId,
      dateTime: {
        $gte: startDate,
        $lte: endDate,
      },
    };

    const rateCards = await RateCard.find(query).populate('packageId');

    ResponseUtil.success(res, 'Rate cards retrieved successfully', rateCards);
  });
}
