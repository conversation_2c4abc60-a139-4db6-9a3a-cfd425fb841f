import { IRateCard, IRequest, IResponse } from '../types';
import RateCard from '../models/rate-card.model';
import UnitTypeModel from '../models/unit-type.model';
import PackageModel from '../models/package.model';
import { ResponseUtil } from '../utils/response';
import { asyncRequestHandler } from '../middlewares/async-request-handler';
import mongoose from 'mongoose';

export class RateCardController {
  private static Instance: RateCardController;

  public static getInstance(): RateCardController {
    if (!RateCardController.Instance) {
      RateCardController.Instance = new RateCardController();
    }
    return RateCardController.Instance;
  }

  private constructor() {}

  create = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { unitId, fromDate, toDate, days, fromTime, toTime, percentageChange } = req.body;
    const { propertyId } = req.params;

    if (!unitId || !fromDate || !toDate || !days || percentageChange === undefined) {
      return ResponseUtil.badRequest(res, 'Missing required fields: unitId, fromDate, toDate, days, percentageChange');
    }

    try {
      // Validate unit exists
      const unitType = await UnitTypeModel.findById(unitId);
      if (!unitType) {
        return ResponseUtil.badRequest(res, 'Unit type not found');
      }

      // Handle overlapping rules by archiving them
      await RateCard.updateMany(
        {
          unitId,
          isActive: true,
          fromDate: { $lte: new Date(toDate) },
          toDate: { $gte: new Date(fromDate) },
          $or: [
            { days: { $in: days.map((day: string) => day.toLowerCase()) } },
            { days: 'all' },
            { $expr: { $in: ['all', days.map((day: string) => day.toLowerCase())] } },
          ],
        },
        {
          $set: {
            isActive: false,
            archivedAt: new Date(),
            archivedReason: 'Overridden by new rule',
          },
        },
      );

      // Create the new rule
      const newRule = new RateCard({
        propertyId,
        unitId,
        fromDate: new Date(fromDate),
        toDate: new Date(toDate),
        days: days.map((day: string) => day.toLowerCase()),
        fromTime: fromTime || '00:00',
        toTime: toTime || '23:59',
        percentageChange,
        isActive: true,
        version: 1,
      });

      const savedRule = await newRule.save();
      ResponseUtil.success(res, 'Rate card rule created successfully', savedRule);
    } catch (error) {
      ResponseUtil.badRequest(res, error instanceof Error ? error.message : 'Failed to create rate card rule');
    }
  });

  getAll = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { propertyId } = req.params;

    try {
      const rateCards = await RateCard.find({
        propertyId,
        isActive: true,
      })
        .populate('unitId', 'name code')
        .sort({ fromDate: 1 });

      ResponseUtil.success(res, 'Rate cards retrieved successfully', rateCards);
    } catch (error) {
      ResponseUtil.badRequest(res, error instanceof Error ? error.message : 'Failed to retrieve rate cards');
    }
  });

  getOne = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { ruleId } = req.params;

    try {
      const rateCard = await RateCard.findOne({
        _id: ruleId,
        isActive: true,
      }).populate('unitId', 'name code');

      if (!rateCard) {
        return ResponseUtil.badRequest(res, 'Rate card rule not found');
      }

      ResponseUtil.success(res, 'Rate card retrieved successfully', rateCard);
    } catch (error) {
      ResponseUtil.badRequest(res, error instanceof Error ? error.message : 'Failed to retrieve rate card');
    }
  });

  updateOne = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { ruleId } = req.params;
    const { unitId, fromDate, toDate, days, fromTime, toTime, percentageChange } = req.body;

    try {
      const rateCard = await RateCard.findOne({
        _id: ruleId,
        isActive: true,
      });

      if (!rateCard) {
        return ResponseUtil.badRequest(res, 'Rate card rule not found');
      }

      // Validate unit exists if unitId is being updated
      if (unitId && unitId !== rateCard.unitId?.toString()) {
        const unitType = await UnitTypeModel.findById(unitId);
        if (!unitType) {
          return ResponseUtil.badRequest(res, 'Unit type not found');
        }
      }

      // Update fields
      if (unitId) rateCard.unitId = unitId;
      if (fromDate) rateCard.fromDate = new Date(fromDate);
      if (toDate) rateCard.toDate = new Date(toDate);
      if (days) rateCard.days = days.map((day: string) => day.toLowerCase());
      if (fromTime) rateCard.fromTime = fromTime;
      if (toTime) rateCard.toTime = toTime;
      if (percentageChange !== undefined) rateCard.percentageChange = percentageChange;

      const updatedRule = await rateCard.save();
      ResponseUtil.success(res, 'Rate card rule updated successfully', updatedRule);
    } catch (error) {
      ResponseUtil.badRequest(res, error instanceof Error ? error.message : 'Failed to update rate card rule');
    }
  });

  getRatesByDate = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { date, unitId, packageId } = req.query;
    const { propertyId } = req.params;

    if (!date) {
      return ResponseUtil.badRequest(res, 'Please provide date parameter');
    }

    try {
      const queryDate = new Date(date as string);
      const dayName = queryDate.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();

      const query: mongoose.FilterQuery<IRateCard> = {
        propertyId,
        isActive: true,
        fromDate: { $lte: queryDate },
        toDate: { $gte: queryDate },
        $or: [{ days: 'all' }, { days: dayName }],
      };

      if (unitId) {
        query.unitId = unitId;
      }

      const rateCards = await RateCard.find(query).populate('unitId', 'name code totalUnits').sort({ createdAt: -1 });

      if (packageId) {
        const packageDoc = await PackageModel.findById(packageId);
        if (!packageDoc) {
          return ResponseUtil.badRequest(res, 'Package not found');
        }

        const ratesWithCalculation = rateCards.map((rule) => {
          const baseRate = packageDoc.price;
          const adjustedRate = rule.percentageChange ? baseRate * (1 + rule.percentageChange / 100) : baseRate;

          return {
            ...rule.toObject(),
            baseRate,
            adjustedRate,
            packageInfo: {
              _id: packageDoc._id,
              name: packageDoc.name,
              price: packageDoc.price,
            },
          };
        });

        ResponseUtil.success(res, 'Rates with calculation retrieved successfully', ratesWithCalculation);
      } else {
        ResponseUtil.success(res, 'Rate cards retrieved successfully', rateCards);
      }
    } catch (error) {
      ResponseUtil.badRequest(res, error instanceof Error ? error.message : 'Failed to retrieve rates');
    }
  });
}
