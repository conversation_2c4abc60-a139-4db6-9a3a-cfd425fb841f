import { IRateCard, IRequest, IResponse } from '../types';
import RateCard from '../models/rate-card.model';
import { ResponseUtil } from '../utils/response';
import { asyncRequestHandler } from '../middlewares/async-request-handler';
import { RateCardRuleService } from '../services/rate-card-rule.service';

export class RateCardController {
  private static Instance: RateCardController;
  private rateCardRuleService: RateCardRuleService;

  public static getInstance(): RateCardController {
    if (!RateCardController.Instance) {
      RateCardController.Instance = new RateCardController();
    }
    return RateCardController.Instance;
  }

  private constructor() {
    this.rateCardRuleService = RateCardRuleService.getInstance();
  }

  create = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const rateCards = req.body.rateCards;

    // Prepare bulk operations with upsert
    const bulkOperations = rateCards.map((rateCard: IRateCard) => ({
      updateOne: {
        filter: {
          packageId: rateCard.packageId,
          dateTime: rateCard.dateTime,
          propertyId: req.params.propertyId,
        },
        update: { $set: rateCard },
        upsert: true,
      },
    }));

    const result = await RateCard.bulkWrite(bulkOperations);

    ResponseUtil.success(res, 'Rate cards created/updated successfully', result);
  });

  getByDate = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { date } = req.query;
    const { propertyId } = req.params;

    if (!date) {
      return ResponseUtil.badRequest(res, 'Please provide date parameter');
    }

    const startDate = new Date(date as string);
    startDate.setHours(0, 0, 0, 0);

    const endDate = new Date(date as string);
    endDate.setHours(23, 59, 59, 999);

    const query = {
      propertyId: propertyId,
      dateTime: {
        $gte: startDate,
        $lte: endDate,
      },
    };

    const rateCards = await RateCard.find(query).populate('packageId');

    ResponseUtil.success(res, 'Rate cards retrieved successfully', rateCards);
  });

  // New rule-based endpoints

  /**
   * Create a new rate card rule
   */
  createRule = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { unitId, fromDate, toDate, days, fromTime, toTime, percentageChange } = req.body;

    if (!unitId || !fromDate || !toDate || !days || percentageChange === undefined) {
      return ResponseUtil.badRequest(res, 'Missing required fields: unitId, fromDate, toDate, days, percentageChange');
    }

    try {
      const result = await this.rateCardRuleService.createRule({
        unitId,
        fromDate: new Date(fromDate),
        toDate: new Date(toDate),
        days,
        fromTime,
        toTime,
        percentageChange
      });

      ResponseUtil.success(res, 'Rate card rule created successfully', result);
    } catch (error) {
      ResponseUtil.badRequest(res, error instanceof Error ? error.message : 'Failed to create rate card rule');
    }
  });

  /**
   * Get rate resolution for a booking query
   */
  resolveRate = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { unitId, packageId, startDateTime, endDateTime } = req.query;

    if (!unitId || !packageId || !startDateTime || !endDateTime) {
      return ResponseUtil.badRequest(res, 'Missing required query parameters: unitId, packageId, startDateTime, endDateTime');
    }

    try {
      const result = await this.rateCardRuleService.resolveRate(
        unitId as string,
        packageId as string,
        new Date(startDateTime as string),
        new Date(endDateTime as string)
      );

      ResponseUtil.success(res, 'Rate resolved successfully', result);
    } catch (error) {
      ResponseUtil.badRequest(res, error instanceof Error ? error.message : 'Failed to resolve rate');
    }
  });

  /**
   * Get all active rules for a unit
   */
  getRulesForUnit = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { unitId } = req.params;

    if (!unitId) {
      return ResponseUtil.badRequest(res, 'Unit ID is required');
    }

    try {
      const rules = await this.rateCardRuleService.getRulesForUnit(unitId);
      ResponseUtil.success(res, 'Rate card rules retrieved successfully', rules);
    } catch (error) {
      ResponseUtil.badRequest(res, error instanceof Error ? error.message : 'Failed to retrieve rate card rules');
    }
  });

  /**
   * Archive a rate card rule
   */
  archiveRule = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { ruleId } = req.params;
    const { reason } = req.body;

    if (!ruleId) {
      return ResponseUtil.badRequest(res, 'Rule ID is required');
    }

    try {
      const archivedRule = await this.rateCardRuleService.archiveRule(ruleId, reason);
      ResponseUtil.success(res, 'Rate card rule archived successfully', archivedRule);
    } catch (error) {
      ResponseUtil.badRequest(res, error instanceof Error ? error.message : 'Failed to archive rate card rule');
    }
  });
}
