import { IPackage, IRateCard, IRequest, IResponse } from '../types';
import RateCard from '../models/rate-card.model';
import PackageModel from '../models/package.model';
import { ResponseUtil } from '../utils/response';
import { asyncRequestHandler } from '../middlewares/async-request-handler';
import mongoose from 'mongoose';

export class RateCardController {
  private static Instance: RateCardController;

  public static getInstance(): RateCardController {
    if (!RateCardController.Instance) {
      RateCardController.Instance = new RateCardController();
    }
    return RateCardController.Instance;
  }

  private constructor() {}

  create = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { packageId, fromDate, toDate, days, fromTime, toTime, percentageChange } = req.body;
    const { propertyId } = req.params;

    if (!packageId || !fromDate || !toDate || !days || percentageChange === undefined) {
      return ResponseUtil.badRequest(res, 'Missing required fields: packageId, fromDate, toDate, days, percentageChange');
    }

    try {
      // Validate package exists
      const packageDoc = await PackageModel.findById(packageId);
      if (!packageDoc) {
        return ResponseUtil.badRequest(res, 'Package not found');
      }

      // Handle overlapping rules by archiving them
      await RateCard.updateMany(
        {
          packageId,
          isActive: true,
          fromDate: { $lte: new Date(toDate) },
          toDate: { $gte: new Date(fromDate) },
          $or: [
            { days: { $in: days.map((day: string) => day.toLowerCase()) } },
            { days: 'all' },
            { $expr: { $in: ['all', days.map((day: string) => day.toLowerCase())] } },
          ],
        },
        {
          $set: {
            isActive: false,
            archivedAt: new Date(),
            archivedReason: 'Overridden by new rule',
          },
        },
      );

      // Create the new rule
      const newRule = new RateCard({
        propertyId,
        packageId,
        fromDate: new Date(fromDate),
        toDate: new Date(toDate),
        days: days.map((day: string) => day.toLowerCase()),
        fromTime: fromTime || '00:00',
        toTime: toTime || '23:59',
        percentageChange,
        isActive: true,
        version: 1,
      });

      const savedRule = await newRule.save();
      ResponseUtil.success(res, 'Rate card rule created successfully', savedRule);
    } catch (error) {
      ResponseUtil.badRequest(res, error instanceof Error ? error.message : 'Failed to create rate card rule');
    }
  });

  getAll = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { propertyId } = req.params;

    try {
      const rateCards = await RateCard.find({
        propertyId,
        isActive: true,
      })
        .populate('packageId', 'name price')
        .sort({ fromDate: 1 });

      ResponseUtil.success(res, 'Rate cards retrieved successfully', rateCards);
    } catch (error) {
      ResponseUtil.badRequest(res, error instanceof Error ? error.message : 'Failed to retrieve rate cards');
    }
  });

  getOne = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { ruleId } = req.params;

    try {
      const rateCard = await RateCard.findOne({
        _id: ruleId,
        isActive: true,
      }).populate('packageId', 'name price');

      if (!rateCard) {
        return ResponseUtil.badRequest(res, 'Rate card rule not found');
      }

      ResponseUtil.success(res, 'Rate card retrieved successfully', rateCard);
    } catch (error) {
      ResponseUtil.badRequest(res, error instanceof Error ? error.message : 'Failed to retrieve rate card');
    }
  });

  updateOne = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { ruleId } = req.params;
    const { packageId, fromDate, toDate, days, fromTime, toTime, percentageChange } = req.body;

    try {
      const rateCard = await RateCard.findOne({
        _id: ruleId,
        isActive: true,
      });

      if (!rateCard) {
        return ResponseUtil.badRequest(res, 'Rate card rule not found');
      }

      // Validate package exists if packageId is being updated
      if (packageId && packageId !== rateCard.packageId?.toString()) {
        const packageDoc = await PackageModel.findById(packageId);
        if (!packageDoc) {
          return ResponseUtil.badRequest(res, 'Package not found');
        }
      }

      // Update fields
      if (packageId) rateCard.packageId = packageId;
      if (fromDate) rateCard.fromDate = new Date(fromDate);
      if (toDate) rateCard.toDate = new Date(toDate);
      if (days) rateCard.days = days.map((day: string) => day.toLowerCase());
      if (fromTime) rateCard.fromTime = fromTime;
      if (toTime) rateCard.toTime = toTime;
      if (percentageChange !== undefined) rateCard.percentageChange = percentageChange;

      const updatedRule = await rateCard.save();
      ResponseUtil.success(res, 'Rate card rule updated successfully', updatedRule);
    } catch (error) {
      ResponseUtil.badRequest(res, error instanceof Error ? error.message : 'Failed to update rate card rule');
    }
  });

  getRatesByDate = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { date, packageId } = req.query;
    const { propertyId } = req.params;

    if (!date) {
      return ResponseUtil.badRequest(res, 'Please provide date parameter');
    }

    try {
      const queryDate = new Date(date as string);
      const dayName = queryDate.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();

      const query: mongoose.FilterQuery<IRateCard> = {
        propertyId,
        isActive: true,
        fromDate: { $lte: queryDate },
        toDate: { $gte: queryDate },
        $or: [{ days: 'all' }, { days: dayName }],
      };

      if (packageId) {
        query.packageId = packageId;
      }

      const rateCards = await RateCard.find(query).populate('packageId', 'name price').sort({ createdAt: -1 });

      // Calculate adjusted rates
      const ratesWithCalculation = rateCards.map((rule) => {
        const packageInfo = rule.packageId as unknown as IPackage;
        const baseRate = packageInfo?.price || 0;
        const adjustedRate = rule.percentageChange ? baseRate * (1 + rule.percentageChange / 100) : baseRate;

        return {
          ...rule.toObject(),
          baseRate,
          adjustedRate,
        };
      });

      ResponseUtil.success(res, 'Rates retrieved successfully', ratesWithCalculation);
    } catch (error) {
      ResponseUtil.badRequest(res, error instanceof Error ? error.message : 'Failed to retrieve rates');
    }
  });
}
