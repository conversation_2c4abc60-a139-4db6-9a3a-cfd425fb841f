import { IRateCard, IRequest, IResponse } from '../types';
import RateCard from '../models/rate-card.model';
import UnitTypeModel from '../models/unit-type.model';
import PackageModel from '../models/package.model';
import ReservationItemModel from '../models/reservation-item.model';
import { ResponseUtil } from '../utils/response';
import { asyncRequestHandler } from '../middlewares/async-request-handler';
import mongoose from 'mongoose';

export class RateCardController {
  private static Instance: RateCardController;

  public static getInstance(): RateCardController {
    if (!RateCardController.Instance) {
      RateCardController.Instance = new RateCardController();
    }
    return RateCardController.Instance;
  }

  private constructor() {}

  // Legacy create method (keeping for backward compatibility)
  create = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const rateCards = req.body.rateCards;

    // Prepare bulk operations with upsert
    const bulkOperations = rateCards.map((rateCard: IRateCard) => ({
      updateOne: {
        filter: {
          packageId: rateCard.packageId,
          dateTime: rateCard.dateTime,
          propertyId: req.params.propertyId,
        },
        update: { $set: rateCard },
        upsert: true,
      },
    }));

    const result = await RateCard.bulkWrite(bulkOperations);

    ResponseUtil.success(res, 'Rate cards created/updated successfully', result);
  });

  // Legacy getByDate method (keeping for backward compatibility)
  getByDate = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { date } = req.query;
    const { propertyId } = req.params;

    if (!date) {
      return ResponseUtil.badRequest(res, 'Please provide date parameter');
    }

    const startDate = new Date(date as string);
    startDate.setHours(0, 0, 0, 0);

    const endDate = new Date(date as string);
    endDate.setHours(23, 59, 59, 999);

    const query = {
      propertyId: propertyId,
      dateTime: {
        $gte: startDate,
        $lte: endDate,
      },
    };

    const rateCards = await RateCard.find(query).populate('packageId');

    ResponseUtil.success(res, 'Rate cards retrieved successfully', rateCards);
  });

  // New rule-based endpoints

  /**
   * Create a new rate card rule
   */
  createRule = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { unitId, fromDate, toDate, days, fromTime, toTime, percentageChange } = req.body;
    const { propertyId } = req.params;

    if (!unitId || !fromDate || !toDate || !days || percentageChange === undefined) {
      return ResponseUtil.badRequest(res, 'Missing required fields: unitId, fromDate, toDate, days, percentageChange');
    }

    try {
      const result = await this.createRateCardRule({
        propertyId,
        unitId,
        fromDate: new Date(fromDate),
        toDate: new Date(toDate),
        days,
        fromTime: fromTime || '00:00',
        toTime: toTime || '23:59',
        percentageChange
      });

      ResponseUtil.success(res, 'Rate card rule created successfully', result);
    } catch (error) {
      ResponseUtil.badRequest(res, error instanceof Error ? error.message : 'Failed to create rate card rule');
    }
  });

  /**
   * Get rate resolution for a booking query
   */
  resolveRate = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { unitId, packageId, startDateTime, endDateTime } = req.query;

    if (!unitId || !packageId || !startDateTime || !endDateTime) {
      return ResponseUtil.badRequest(res, 'Missing required query parameters: unitId, packageId, startDateTime, endDateTime');
    }

    try {
      const result = await this.resolveRateForBooking(
        unitId as string,
        packageId as string,
        new Date(startDateTime as string),
        new Date(endDateTime as string)
      );

      ResponseUtil.success(res, 'Rate resolved successfully', result);
    } catch (error) {
      ResponseUtil.badRequest(res, error instanceof Error ? error.message : 'Failed to resolve rate');
    }
  });

  /**
   * Get all active rules for a unit
   */
  getRulesForUnit = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { unitId } = req.params;

    if (!unitId) {
      return ResponseUtil.badRequest(res, 'Unit ID is required');
    }

    try {
      const rules = await RateCard.find({
        unitId,
        isActive: true
      }).sort({ fromDate: 1 });

      ResponseUtil.success(res, 'Rate card rules retrieved successfully', rules);
    } catch (error) {
      ResponseUtil.badRequest(res, error instanceof Error ? error.message : 'Failed to retrieve rate card rules');
    }
  });

  /**
   * Archive a rate card rule
   */
  archiveRule = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { ruleId } = req.params;
    const { reason } = req.body;

    if (!ruleId) {
      return ResponseUtil.badRequest(res, 'Rule ID is required');
    }

    try {
      const rule = await RateCard.findById(ruleId);
      if (!rule) {
        return ResponseUtil.badRequest(res, 'Rule not found');
      }

      rule.isActive = false;
      rule.archivedAt = new Date();
      rule.archivedReason = reason || 'Manually archived';

      const archivedRule = await rule.save();
      ResponseUtil.success(res, 'Rate card rule archived successfully', archivedRule);
    } catch (error) {
      ResponseUtil.badRequest(res, error instanceof Error ? error.message : 'Failed to archive rate card rule');
    }
  });

  // Helper methods for rule management

  /**
   * Create a rate card rule with overlap detection and resolution
   */
  private async createRateCardRule(ruleInput: {
    propertyId: string;
    unitId: string;
    fromDate: Date;
    toDate: Date;
    days: string[];
    fromTime: string;
    toTime: string;
    percentageChange: number;
  }) {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Validate unit exists
      const unitType = await UnitTypeModel.findById(ruleInput.unitId).session(session);
      if (!unitType) {
        throw new Error('Unit type not found');
      }

      // Find overlapping rules
      const overlappingRules = await this.findOverlappingRules(ruleInput, session);

      // Create the new rule
      const newRule = new RateCard({
        propertyId: ruleInput.propertyId,
        unitId: ruleInput.unitId,
        fromDate: ruleInput.fromDate,
        toDate: ruleInput.toDate,
        days: ruleInput.days.map(day => day.toLowerCase()),
        fromTime: ruleInput.fromTime,
        toTime: ruleInput.toTime,
        percentageChange: ruleInput.percentageChange,
        isActive: true,
        version: 1
      });

      // Handle overlaps by archiving old rules
      for (const existingRule of overlappingRules) {
        existingRule.isActive = false;
        existingRule.archivedAt = new Date();
        existingRule.archivedReason = 'Overridden by new rule';
        await existingRule.save({ session });

        if (this.isExactMatch(existingRule, newRule)) {
          newRule.version = (existingRule.version || 1) + 1;
        }
      }

      await newRule.save({ session });
      await session.commitTransaction();

      return {
        action: overlappingRules.length > 0 ? 'override' : 'create',
        affectedRules: overlappingRules,
        newRule
      };
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Find overlapping rules for a given rule input
   */
  private async findOverlappingRules(
    ruleInput: any,
    session?: mongoose.ClientSession
  ): Promise<IRateCard[]> {
    const query = {
      unitId: ruleInput.unitId,
      isActive: true,
      fromDate: { $lte: ruleInput.toDate },
      toDate: { $gte: ruleInput.fromDate },
      $or: [
        { days: { $in: ruleInput.days.map((day: string) => day.toLowerCase()) } },
        { days: 'all' },
        { $expr: { $in: ['all', ruleInput.days.map((day: string) => day.toLowerCase())] } }
      ]
    };

    const options = session ? { session } : {};
    return await RateCard.find(query, null, options);
  }

  /**
   * Check if two rules are an exact match
   */
  private isExactMatch(rule1: IRateCard, rule2: IRateCard): boolean {
    return (
      rule1.fromDate?.getTime() === rule2.fromDate?.getTime() &&
      rule1.toDate?.getTime() === rule2.toDate?.getTime() &&
      rule1.fromTime === rule2.fromTime &&
      rule1.toTime === rule2.toTime &&
      JSON.stringify(rule1.days?.sort()) === JSON.stringify(rule2.days?.sort())
    );
  }

  /**
   * Resolve rate for a booking query
   */
  private async resolveRateForBooking(
    unitId: string,
    packageId: string,
    startDateTime: Date,
    endDateTime: Date
  ) {
    // Get base rate from package
    const packageDoc = await PackageModel.findById(packageId);
    if (!packageDoc) {
      throw new Error('Package not found');
    }

    const baseHourlyRate = packageDoc.price; // Assuming price is per hour
    const hourlyBreakdown: Array<{
      hour: Date;
      baseRate: number;
      appliedRule?: IRateCard;
      adjustedRate: number;
    }> = [];
    let totalRate = 0;

    // Generate hourly slots
    const currentHour = new Date(startDateTime);
    currentHour.setMinutes(0, 0, 0); // Round to hour

    while (currentHour < endDateTime) {
      // Find the most recent active rule that matches this hour
      const applicableRule = await this.findApplicableRule(unitId, currentHour);

      let adjustedRate = baseHourlyRate;
      if (applicableRule && applicableRule.percentageChange !== undefined) {
        adjustedRate = baseHourlyRate * (1 + applicableRule.percentageChange / 100);
      }

      hourlyBreakdown.push({
        hour: new Date(currentHour),
        baseRate: baseHourlyRate,
        appliedRule: applicableRule || undefined,
        adjustedRate
      });

      totalRate += adjustedRate;
      currentHour.setHours(currentHour.getHours() + 1);
    }

    return {
      totalRate,
      hourlyBreakdown
    };
  }

  /**
   * Find the most applicable rule for a specific date and time
   */
  private async findApplicableRule(unitId: string, dateTime: Date): Promise<IRateCard | null> {
    const dayName = dateTime.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
    const timeString = dateTime.toTimeString().substring(0, 5); // HH:MM format

    const rules = await RateCard.find({
      unitId,
      isActive: true,
      fromDate: { $lte: dateTime },
      toDate: { $gte: dateTime },
      $or: [
        { days: 'all' },
        { days: dayName }
      ],
      fromTime: { $lte: timeString },
      toTime: { $gte: timeString }
    }).sort({ createdAt: -1 }); // Most recent first

    return rules.length > 0 ? rules[0] : null;
  }
}
