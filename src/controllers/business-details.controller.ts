import { IBusinessDetails, IRequest, IResponse } from '../types';
import mongoose from 'mongoose';
import BusinessDetailsSchema from '../models/business-details.model';
import { ResponseUtil } from '../utils/response';
import { asyncRequestHandler } from '../middlewares/async-request-handler';

export class BusinessDetailsController {
  private static Instance: BusinessDetailsController;
  private constructor() {}
  public static getInstance(): BusinessDetailsController {
    if (!BusinessDetailsController.Instance) {
      BusinessDetailsController.Instance = new BusinessDetailsController();
    }
    return BusinessDetailsController.Instance;
  }

  create = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { propertyId } = req.params;
    const businessDetails = {
      ...req.body,
      propertyId: new mongoose.Types.ObjectId(propertyId),
    };

    const updatedBusiness = await BusinessDetailsSchema.findOneAndUpdate(
      { propertyId: new mongoose.Types.ObjectId(propertyId) },
      { $set: businessDetails },
      {
        new: true,
        upsert: true,
        runValidators: true,
      },
    );
    await updatedBusiness.populate([
      {
        path: 'commission.frequency',
        select: 'name',
      },
      {
        path: 'serviceCharges',
      },
      {
        path: 'propertyId',
        populate: [
          {
            path: 'address.locationId',
          },
          {
            path: 'serviceType',
          },
          {
            path: 'owner',
            select: '-password -createdAt -updatedAt -__v -role',
          },
        ],
      },
    ]);

    ResponseUtil.success(res, 'Business details upserted successfully', updatedBusiness);
  });

  getAll = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { propertyId } = req.params;
    const query: mongoose.FilterQuery<IBusinessDetails> = { propertyId };

    const businessDetails = await BusinessDetailsSchema.findOne(query).populate([
      {
        path: 'commission.frequency',
        select: 'name',
      },
      {
        path: 'serviceCharges',
        select: 'name',
      },
      {
        path: 'propertyId',
        populate: [
          {
            path: 'address.locationId',
          },
          {
            path: 'serviceType',
          },
          {
            path: 'owner',
            select: '-password -createdAt -updatedAt -__v -role',
          },
        ],
      },
      {
        path: 'salesTax.name',
        select: 'name',
      },
    ]);
    ResponseUtil.success(res, 'Business details retrieved successfully', businessDetails);
  });

  get = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const businessDetails = await BusinessDetailsSchema.findById(req.params.id).populate([
      {
        path: 'frequency',
      },
      {
        path: 'serviceCharges',
      },
      {
        path: 'propertyId',
        populate: [
          {
            path: 'address.locationId',
          },
          {
            path: 'serviceType',
          },
          {
            path: 'owner',
            select: '-password -createdAt -updatedAt -__v -role',
          },
        ],
      },
    ]);
    if (!businessDetails) {
      return ResponseUtil.notFound(res, 'Business details not found');
    }
    ResponseUtil.success(res, 'Business details retrieved successfully', businessDetails);
  });

  update = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const businessDetails = await BusinessDetailsSchema.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
    }).populate([
      {
        path: 'frequency',
      },
      {
        path: 'serviceCharges',
      },
      {
        path: 'propertyId',
        populate: [
          {
            path: 'address.locationId',
          },
          {
            path: 'serviceType',
          },
          {
            path: 'owner',
            select: '-password -createdAt -updatedAt -__v -role',
          },
        ],
      },
    ]);
    if (!businessDetails) {
      return ResponseUtil.notFound(res, 'Business details not found');
    }
    ResponseUtil.success(res, 'Business details updated successfully', businessDetails);
  });

  delete = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const businessDetails = await BusinessDetailsSchema.findByIdAndDelete(req.params.id);
    if (!businessDetails) {
      return ResponseUtil.notFound(res, 'Business details not found');
    }
    ResponseUtil.success(res, 'Business details deleted successfully');
  });
}
