import { IPackage, IRequest, IResponse } from '../types';
import Package from '../models/package.model';
import { ResponseUtil } from '../utils/response';
import { asyncRequestHandler } from '../middlewares/async-request-handler';

export class PackageController {
  private static Instance: PackageController;

  public static getInstance(): PackageController {
    if (!PackageController.Instance) {
      PackageController.Instance = new PackageController();
    }
    return PackageController.Instance;
  }

  private constructor() {}

  getAll = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const packages: IPackage[] = await Package.find(
      {
        propertyId: req.params.propertyId,
      },
      {},
      { populate: ['unitTypeId', 'amenities', 'taxes'] },
    );
    ResponseUtil.success(res, 'Packages retrieved successfully', packages);
  });

  get = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const pkg = await Package.findById(req.params.id);
    if (!pkg) {
      return ResponseUtil.notFound(res, 'Package not found');
    }
    ResponseUtil.success(res, 'Package retrieved successfully', pkg);
  });

  create = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const propertyId = req.params.propertyId;

    const pkg: IPackage = req.body;
    const existingPackage = await Package.findOne({
      propertyId,
      name: pkg.name,
    });
    if (existingPackage) {
      return ResponseUtil.conflict(res, 'Package already exists');
    }

    const newPackage = new Package({
      ...pkg,
      propertyId,
    });

    await newPackage.save();
    ResponseUtil.created(res, 'Package created successfully', newPackage);
  });

  update = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const pkg = await Package.findById(req.params.id);
    if (!pkg) {
      return ResponseUtil.notFound(res, 'Package not found');
    }

    const updatedPackage = await Package.findByIdAndUpdate(req.params.id, req.body, { new: true });

    ResponseUtil.success(res, 'Package updated successfully', updatedPackage);
  });

  delete = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const pkg = await Package.findByIdAndDelete(req.params.id);

    if (!pkg) {
      return ResponseUtil.notFound(res, 'Package not found');
    }

    ResponseUtil.success(res, 'Package deleted successfully', null, 204);
  });
}
