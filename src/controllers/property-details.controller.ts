import { IPropertyDetails, IRequest, IResponse } from '../types';
import PropertyDetailsSchema from '../models/property-details.model';
import mongoose from 'mongoose';
import { ResponseUtil } from '../utils/response';
import { asyncRequestHandler } from '../middlewares/async-request-handler';

export class PropertyDetailsController {
  private static Instance: PropertyDetailsController;
  private constructor() {}
  public static getInstance(): PropertyDetailsController {
    if (!PropertyDetailsController.Instance) {
      PropertyDetailsController.Instance = new PropertyDetailsController();
    }
    return PropertyDetailsController.Instance;
  }

  create = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { propertyId } = req.params;
    const propertyDetails = {
      ...req.body,
      propertyId: new mongoose.Types.ObjectId(propertyId),
    };

    const updatedProperty = await PropertyDetailsSchema.findOneAndUpdate(
      { propertyId: new mongoose.Types.ObjectId(propertyId) },
      { $set: propertyDetails },
      {
        new: true,
        upsert: true,
        runValidators: true,
      },
    );

    await updatedProperty.populate([
      {
        path: 'amenities',
      },
      {
        path: 'currency',
      },
      {
        path: 'propertyId',
        populate: [
          {
            path: 'address.locationId',
          },
          {
            path: 'serviceType',
          },
          {
            path: 'owner',
            select: '-password -createdAt -updatedAt -__v -role',
          },
        ],
      },
    ]);

    ResponseUtil.success(res, 'Property details upserted successfully', updatedProperty);
  });

  createAmenities = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { propertyId } = req.params;
    const { amenities } = req.body;

    let updateQuery: mongoose.UpdateQuery<IPropertyDetails> = {};

    if (Array.isArray(amenities)) {
      if (amenities.length === 0) {
        updateQuery = { $set: { amenities: [] } };
      } else {
        updateQuery = { $set: { amenities } };
      }
    }

    const propertyDetails = await PropertyDetailsSchema.findOneAndUpdate(
      { propertyId: new mongoose.Types.ObjectId(propertyId) },
      updateQuery,
      { new: true, runValidators: true },
    );

    ResponseUtil.success(res, 'Property details updated successfully', propertyDetails);
  });

  getAll = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { propertyId } = req.params;
    const query: mongoose.FilterQuery<IPropertyDetails> = { propertyId };

    const propertyDetails = await PropertyDetailsSchema.findOne(query).populate([
      {
        path: 'amenities',
      },
      {
        path: 'currency',
      },
      {
        path: 'propertyId',
        populate: [
          {
            path: 'address.locationId',
          },
          {
            path: 'serviceType',
          },
          {
            path: 'owner',
            select: '-password -createdAt -updatedAt -__v -role',
          },
        ],
      },
    ]);
    ResponseUtil.success(res, 'Property details retrieved successfully', propertyDetails);
  });

  get = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const propertyDetails = await PropertyDetailsSchema.findById(req.params.id).populate([
      {
        path: 'amenities',
      },
      {
        path: 'currency',
      },
      {
        path: 'propertyId',
        populate: [
          {
            path: 'address.locationId',
          },
          {
            path: 'serviceType',
          },
          {
            path: 'owner',
          },
        ],
      },
    ]);
    if (!propertyDetails) {
      return ResponseUtil.notFound(res, 'Property details not found');
    }
    ResponseUtil.success(res, 'Property details retrieved successfully', propertyDetails);
  });

  update = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const propertyDetails = await PropertyDetailsSchema.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
    }).populate([
      {
        path: 'amenities',
      },
      {
        path: 'currency',
      },
      {
        path: 'propertyId',
        populate: [
          {
            path: 'address.locationId',
          },
          {
            path: 'serviceType',
          },
          {
            path: 'owner',
          },
        ],
      },
    ]);
    if (!propertyDetails) {
      return ResponseUtil.notFound(res, 'Property details not found');
    }
    ResponseUtil.success(res, 'Property details updated successfully', propertyDetails);
  });

  delete = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const deletedDetails = await PropertyDetailsSchema.findByIdAndDelete(req.params.id);
    if (!deletedDetails) {
      return ResponseUtil.notFound(res, 'Property details not found');
    }
    ResponseUtil.success(res, 'Property details deleted successfully');
  });
}
