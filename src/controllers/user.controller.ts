import User from '../models/user.model';
import { IRequest, IResponse, IUser } from '../types';
import { ResponseUtil } from '../utils/response';
import { asyncRequestHandler } from '../middlewares/async-request-handler';

export class UserController {
  private static Instance: UserController;

  public static getInstance(): UserController {
    if (!UserController.Instance) {
      UserController.Instance = new UserController();
    }
    return UserController.Instance;
  }

  private constructor() {}

  getAllUsers = asyncRequestHandler(async (_req: IRequest, res: IResponse) => {
    const users: IUser[] = await User.find().select('-__v -password');
    ResponseUtil.success(res, 'Users retrieved successfully', users);
  });

  register = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { name, email, password } = req.body;
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return ResponseUtil.conflict(res, 'User already exists');
    }

    const user: IUser = new User({ name, email, password });
    await user.save();
    ResponseUtil.created(res, 'User created successfully', user);
  });

  updateUser = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const userId = req.params.userId;
    const userIdFromToken = req.user?.userId;

    if (userId !== userIdFromToken) {
      return ResponseUtil.forbidden(res, 'User ID mismatch');
    }
    const user = await User.findByIdAndUpdate(userId, req.body, {
      new: true,
    }).select('-__v -password');

    if (!user) {
      return ResponseUtil.notFound(res, 'User not found');
    }
    ResponseUtil.success(res, 'User updated successfully', { ...user.toObject() });
  });
}
