import PropertyPoliciesSchema from '../models/property-policies.model';
import { IPropertyPolicies, IRequest, IResponse } from '../types';
import mongoose from 'mongoose';
import { ResponseUtil } from '../utils/response';
import { asyncRequestHandler } from '../middlewares/async-request-handler';

export class PropertyPolicyController {
  private static Instance: PropertyPolicyController;
  private constructor() {}
  public static getInstance(): PropertyPolicyController {
    if (!PropertyPolicyController.Instance) {
      PropertyPolicyController.Instance = new PropertyPolicyController();
    }
    return PropertyPolicyController.Instance;
  }

  create = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { propertyId } = req.params;

    const updateOperations = {
      $set: {
        propertyId: new mongoose.Types.ObjectId(propertyId),
        updatedAt: new Date(),
      },
      $addToSet: {
        policies: { $each: req.body.policies },
        cancellationPolicies: { $each: req.body.cancellationPolicies },
        customPolicies: { $each: req.body.customPolicies },
      },
    };

    const propertyPolicies = await PropertyPoliciesSchema.findOneAndUpdate(
      { propertyId: new mongoose.Types.ObjectId(propertyId) },
      updateOperations,
      {
        new: true,
        upsert: true,
        runValidators: true,
      },
    );

    ResponseUtil.created(res, 'Property policies upserted successfully', propertyPolicies);
  });

  getAll = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { propertyId } = req.params;
    const query: mongoose.FilterQuery<IPropertyPolicies> = { propertyId };
    const propertyPolicies = await PropertyPoliciesSchema.findOne(query).populate([
      {
        path: 'policies.policyId',
        select: 'name category',
        populate: [
          {
            path: 'category',
            select: 'name',
          },
        ],
      },
      {
        path: 'propertyId',
        select: 'name',
      },
    ]);
    ResponseUtil.success(res, 'Property policies retrieved successfully', propertyPolicies);
  });

  get = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const propertyPolicies = await PropertyPoliciesSchema.findById(req.params.id).populate([
      {
        path: 'policies.policyId',
        select: 'name category',
        populate: [
          {
            path: 'category',
            select: 'name',
          },
        ],
      },
      {
        path: 'propertyId',
        select: 'name',
      },
    ]);
    if (!propertyPolicies) {
      return ResponseUtil.notFound(res, 'Property policies not found');
    }
    ResponseUtil.success(res, 'Property policies retrieved successfully', propertyPolicies);
  });

  update = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const propertyPolicies = await PropertyPoliciesSchema.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!propertyPolicies) {
      return ResponseUtil.notFound(res, 'Property policies not found');
    }
    ResponseUtil.success(res, 'Property policies updated successfully', propertyPolicies);
  });

  delete = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const deletedPolicies = await PropertyPoliciesSchema.findByIdAndDelete(req.params.id);
    if (!deletedPolicies) {
      return ResponseUtil.notFound(res, 'Property policies not found');
    }
    ResponseUtil.success(res, 'Property policies deleted successfully');
  });
}
