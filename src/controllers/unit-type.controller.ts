import { IDomainValue, IRequest, IResponse, IUnitType } from '../types';
import UnitType from '../models/unit-type.model';
import mongoose from 'mongoose';
import PropertyModel from '../models/property.model';
import { ResponseUtil } from '../utils/response';
import { asyncRequestHandler } from '../middlewares/async-request-handler';

export class UnitTypeController {
  private static Instance: UnitTypeController;

  public static getInstance(): UnitTypeController {
    if (!UnitTypeController.Instance) {
      UnitTypeController.Instance = new UnitTypeController();
    }
    return UnitTypeController.Instance;
  }

  private constructor() {}

  getAll = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { propertyId } = req.params;

    // Validate if propertyId is a valid MongoDB ObjectId
    if (!mongoose.Types.ObjectId.isValid(propertyId)) {
      return ResponseUtil.badRequest(res, 'Invalid property ID format');
    }

    const unitTypes: IUnitType[] = await UnitType.find({ propertyId });
    ResponseUtil.success(res, 'Unit types retrieved successfully', unitTypes);
  });

  get = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const unitType = await UnitType.findById(req.params.id);
    if (!unitType) {
      return ResponseUtil.notFound(res, 'Unit type not found');
    }
    ResponseUtil.success(res, 'Unit type retrieved successfully', unitType);
  });

  create = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const propertyId = req.params.propertyId;
    const property = await PropertyModel.findById(propertyId).populate<{
      serviceType: IDomainValue;
    }>('serviceType');
    if (!property) {
      return ResponseUtil.notFound(res, 'Property not found');
    }
    const serviceTypeObj = property.serviceType;

    const unitType: IUnitType = req.body;
    const existingUnitType = await UnitType.findOne({
      propertyId,
      name: unitType.name,
    });
    if (existingUnitType) {
      return ResponseUtil.conflict(res, 'Unit type already exists');
    }

    const newUnitType = new UnitType({
      ...unitType,
      propertyId,
      serviceTypeId: serviceTypeObj._id,
    });

    await newUnitType.save();
    ResponseUtil.created(res, 'Unit type created successfully', newUnitType);
  });

  update = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const existingUnitType = await UnitType.findOne({
      name: req.body.name,
      _id: { $ne: req.params.id },
      propertyId: req.params.propertyId,
    });

    if (existingUnitType) {
      return ResponseUtil.conflict(res, 'Unit type name already exists');
    }

    const updatedUnitType = await UnitType.findByIdAndUpdate(req.params.id, req.body, { new: true });

    ResponseUtil.success(res, 'Unit type updated successfully', updatedUnitType);
  });

  delete = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const unitType = await UnitType.findByIdAndDelete(req.params.id);

    if (!unitType) {
      return ResponseUtil.notFound(res, 'Unit type not found');
    }

    ResponseUtil.success(res, 'Unit type deleted successfully', null, 204);
  });
}
