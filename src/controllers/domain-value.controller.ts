import { DomainValueLevel, IDomainValue, IRequest, IResponse } from '../types';
import DomainValue from '../models/domain-value.model';
import mongoose from 'mongoose';
import { ResponseUtil } from '../utils/response';
import { asyncRequestHandler } from '../middlewares/async-request-handler';

export class DomainValueController {
  private static instance: DomainValueController;

  public static getInstance(): DomainValueController {
    if (!DomainValueController.instance) {
      DomainValueController.instance = new DomainValueController();
    }
    return DomainValueController.instance;
  }

  private constructor() {}

  create = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { name, level, parentId, categoryId } = req.body;
    const { propertyId } = req.params;

    const body = {
      ...req.body,
      propertyId: new mongoose.Types.ObjectId(propertyId),
    };

    const existingDomainValue = await DomainValue.findOne({
      name,
      level,
      parentId,
      propertyId,
      categoryId,
    });
    if (existingDomainValue) {
      return ResponseUtil.conflict(res, 'Domain value already exists');
    }

    const domainValue: IDomainValue = await DomainValue.create(body);
    ResponseUtil.created(res, 'Domain value created successfully', domainValue);
  });

  getAll = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { parentId, serviceTypeId, propertyId, level, q } = req.query;
    const query: mongoose.FilterQuery<IDomainValue> = { active: true, deleted: false };
    if (parentId) {
      query.parentId = new mongoose.Types.ObjectId(parentId as string);
    }
    if (serviceTypeId) {
      query.serviceTypeId = new mongoose.Types.ObjectId(serviceTypeId as string);
    }
    if (propertyId) {
      query.propertyId = new mongoose.Types.ObjectId(propertyId as string);
    }
    if (level) {
      query.level = level;
    }

    if (q) {
      query.name = { $regex: q as string, $options: 'i' };
    }

    const domainValues: IDomainValue[] = await DomainValue.find(query).sort({ updatedAt: 1 });
    ResponseUtil.success(res, 'Domain values retrieved successfully', domainValues);
  });

  getById = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const domainValue = await DomainValue.findById(req.params.id);
    if (!domainValue) {
      return ResponseUtil.notFound(res, 'Domain value not found');
    }
    ResponseUtil.success(res, 'Domain value retrieved successfully', domainValue);
  });

  update = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { propertyId } = req.params;
    const body = {
      ...req.body,
      propertyId: new mongoose.Types.ObjectId(propertyId),
    };
    const domainValue = await DomainValue.findByIdAndUpdate(req.params.id, body, { new: true });
    if (!domainValue) {
      return ResponseUtil.notFound(res, 'Domain value not found');
    }
    ResponseUtil.success(res, 'Domain value updated successfully', domainValue);
  });

  delete = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const domainValue = await DomainValue.findById(req.params.id).select('level');

    if (!domainValue) {
      return ResponseUtil.notFound(res, 'Domain value not found');
    }

    if (domainValue.level === DomainValueLevel.PROPERTY_META || domainValue.level === DomainValueLevel.SYSTEM_META) {
      return ResponseUtil.badRequest(res, 'Cannot delete Meta domain values');
    }
    await DomainValue.findByIdAndDelete(req.params.id);
    ResponseUtil.success(res, 'Domain value deleted successfully', domainValue);
  });
}
