import { IPayment, IRequest, IReservation, IResponse, PaymentStatusEnum, ReservationStatusEnum } from '../types';
import PaymentModel from '../models/payment.model';
import { createCheckoutSession, createStripeRefund, getSessionDetails } from '../services/stripe.service';
import Reservation from '../models/reservation.model';
import ReservationItem from '../models/reservation-item.model';
import { EmailService } from '../services/email.service';
import Property from '../models/property.model';
import { validatePayment, validateRefundInputs } from '../utils/cancellation';
import PropertyPoliciesModel from '../models/property-policies.model';
import { ResponseUtil } from '../utils/response';
import { asyncRequestHandler } from '../middlewares/async-request-handler';
import PaymentProviderEnum from '../types/enums/payment-provider.enum';
import mongoose from 'mongoose';
import { checkReservationAuthorization } from '../utils/reservation';

export class PaymentController {
  private static instance: PaymentController;
  private emailService: EmailService;

  private constructor() {
    this.emailService = new EmailService();
  }

  public static getInstance(): PaymentController {
    if (!PaymentController.instance) {
      PaymentController.instance = new PaymentController();
    }
    return PaymentController.instance;
  }

  get = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const payment = await PaymentModel.findById(req.params.id);
    if (!payment) {
      return ResponseUtil.notFound(res, 'Payment not found');
    }
    ResponseUtil.success(res, 'Payment retrieved successfully', payment);
  });

  create = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const reservation = await Reservation.findById(req.body.reservationId);
    if (!reservation) {
      return ResponseUtil.notFound(res, 'Reservation not found');
    }
    const payment = new PaymentModel({ amount: reservation.grandTotal, ...req.body });
    await payment.save();
    ResponseUtil.created(res, 'Payment created successfully', payment);
  });

  update = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const updatedPayment = await PaymentModel.findByIdAndUpdate(req.params.id, req.body, { new: true });

    if (!updatedPayment) {
      return ResponseUtil.notFound(res, 'Payment not found');
    }

    ResponseUtil.success(res, 'Payment updated successfully', updatedPayment);
  });

  verifyPayment = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    try {
      const payment = await PaymentModel.findById(req.params.id);
      if (!payment) {
        return ResponseUtil.notFound(res, 'Payment not found');
      }

      const cancellationPolicies = await PropertyPoliciesModel.findOne({ propertyId: payment.propertyId })
        .select('cancellationPolicies')
        .lean();

      let reservation: IReservation | null;

      // Handle already processed payments
      if (
        payment.status === PaymentStatusEnum.PAID ||
        payment.status === PaymentStatusEnum.REFUNDED ||
        payment.status === PaymentStatusEnum.FAILED
      ) {
        reservation = await Reservation.findById(payment.reservationId).populate([
          {
            path: 'items',
            populate: [
              { path: 'unitTypeId', select: 'name attachments bedType' },
              { path: 'packageId', select: 'name noOfAdults noOfChildren' },
              { path: 'taxes', select: 'name value' },
            ],
          },
          { path: 'propertyId', select: 'name address', populate: [{ path: 'address.locationId', select: 'name' }] },
        ]);

        if (!reservation) {
          return ResponseUtil.notFound(res, 'Reservation not found');
        }

        return ResponseUtil.success(res, `Payment already marked as ${payment.status}`, {
          ...reservation.toObject(),
          cancellationPolicies: cancellationPolicies ? cancellationPolicies.cancellationPolicies : [],
        });
      }

      // Fetch session details
      const session = await getSessionDetails(payment.paymentGatewayResponse?.sessionId as string);
      if (!session) {
        return ResponseUtil.badRequest(res, 'Invalid or missing session details');
      }

      const transaction = await mongoose.startSession();
      try {
        await transaction.withTransaction(async () => {
          switch (session.payment_status) {
            case 'paid': {
              await PaymentModel.findByIdAndUpdate(
                req.params.id,
                { status: PaymentStatusEnum.PAID },
                { new: true, session: transaction },
              );

              reservation = await Reservation.findByIdAndUpdate(
                payment.reservationId,
                {
                  status: ReservationStatusEnum.CONFIRMED,
                  paymentStatus: PaymentStatusEnum.PAID,
                },
                { new: true, session: transaction },
              ).populate([
                {
                  path: 'items',
                  populate: [
                    { path: 'unitTypeId', select: 'name attachments bedType' },
                    { path: 'packageId', select: 'name noOfAdults noOfChildren' },
                    { path: 'taxes', select: 'name value' },
                  ],
                },
                {
                  path: 'propertyId',
                  select: 'name address',
                  populate: [{ path: 'address.locationId', select: 'name' }],
                },
              ]);

              if (!reservation) {
                throw new Error('Reservation not found during payment confirmation');
              }

              await ReservationItem.updateMany(
                { _id: { $in: reservation.items } },
                { status: ReservationStatusEnum.CONFIRMED, paymentStatus: PaymentStatusEnum.PAID },
                { session: transaction },
              );

              const property = await Property.findById(payment?.propertyId).populate([
                { path: 'address.locationId' },
                { path: 'serviceType' },
                { path: 'owner' },
              ]);

              const propertyPolicies = await PropertyPoliciesModel.findOne({ propertyId: reservation.propertyId });

              if (property && propertyPolicies) {
                try {
                  await this.emailService.sendReservationConfirmationEmail(
                    reservation.bookerDetails.email,
                    reservation.bookerDetails.firstName,
                    reservation,
                    property,
                    propertyPolicies.cancellationPolicies,
                  );
                } catch (emailError) {
                  console.error('Failed to send confirmation email:', emailError);
                }
              }

              return ResponseUtil.success(res, `Payment marked as ${PaymentStatusEnum.PAID}`, {
                ...reservation.toObject(),
                cancellationPolicies: cancellationPolicies ? cancellationPolicies.cancellationPolicies : [],
              });
            }

            case 'unpaid': {
              await PaymentModel.findByIdAndUpdate(
                req.params.id,
                { status: PaymentStatusEnum.FAILED },
                { new: true, session: transaction },
              );

              reservation = await Reservation.findByIdAndUpdate(
                payment.reservationId,
                {
                  status: ReservationStatusEnum.CANCELLED,
                  paymentStatus: PaymentStatusEnum.FAILED,
                },
                { new: true, session: transaction },
              );

              if (reservation) {
                await ReservationItem.updateMany(
                  { _id: { $in: reservation.items } },
                  { status: ReservationStatusEnum.CANCELLED, paymentStatus: PaymentStatusEnum.FAILED },
                  { session: transaction },
                );
              }

              return ResponseUtil.success(res, 'Payment failed', {
                ...reservation?.toObject(),
                cancellationPolicies: cancellationPolicies ? cancellationPolicies.cancellationPolicies : [],
              });
            }

            default:
              throw new Error(`Invalid Stripe session status: ${session.payment_status}`);
          }
        });
      } catch (error) {
        console.error('Transaction failed:', error);
        return ResponseUtil.internalServerError(res, error instanceof Error ? error.message : 'Transaction failed');
      } finally {
        await transaction.endSession();
      }
    } catch (error) {
      console.error('Unexpected error in verifyPayment:', error);
      return ResponseUtil.internalServerError(
        res,
        error instanceof Error ? error.message : 'An unexpected error occurred',
      );
    }
  });

  delete = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const payment = await PaymentModel.findByIdAndDelete(req.params.id);

    if (!payment) {
      return ResponseUtil.notFound(res, 'Payment not found');
    }

    ResponseUtil.success(res, 'Payment deleted successfully', null, 204);
  });

  checkout = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const property = await Property.findById(req.body.propertyId);

    if (!property) {
      return ResponseUtil.notFound(res, 'Property not found');
    }

    const reservation = await Reservation.findById(req.body.reservationId).populate('items');

    if (!reservation) {
      return ResponseUtil.notFound(res, 'Reservation not found');
    }

    const session = await mongoose.startSession();
    try {
      let payment: IPayment;

      await session.withTransaction(async () => {
        const paymentData = await PaymentModel.create(
          [
            {
              reservationId: req.body.reservationId,
              propertyId: req.body.propertyId,
              amount: reservation.grandTotal,
              currency: req.body.currency,
              paymentMethod: req.body.paymentMethod,
            },
          ],
          { session },
        );

        payment = paymentData[0];

        if (req.params.paymentProvider === 'stripe') {
          const response = await createCheckoutSession(payment, reservation as unknown as IReservation);
          await PaymentModel.findByIdAndUpdate(
            payment._id,
            {
              paymentGateway: 'stripe',
              paymentGatewayResponse: response,
            },
            { session },
          );

          return ResponseUtil.success(res, 'Checkout session created successfully', response);
        }
      });
    } catch (error) {
      console.error('Transaction failed:', error);
      ResponseUtil.internalServerError(res, error instanceof Error ? error.message : 'Checkout failed');
    } finally {
      await session.endSession();
    }
  });

  refund = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const user = req.user;
    if (!user) {
      return ResponseUtil.unauthorized(res, 'Unauthorized');
    }
    const { reservationId, paymentProvider } = req.params;
    const cancellationTime = new Date().toISOString();
    const { cancellationReason } = req.body;

    const reservation = await Reservation.findById(reservationId).select('bookerDetails');

    if (!reservation) {
      return ResponseUtil.notFound(res, 'Reservation not found');
    }

    const authCheck = await checkReservationAuthorization(user, reservation);
    if (!authCheck.isAuthorized) {
      return ResponseUtil.unauthorized(res, authCheck.message ?? 'Unauthorized');
    }

    const validationResult = await validateRefundInputs(reservationId, cancellationTime as string, res);
    if (!validationResult) return;

    const { refundPolicy, refundableAmount } = validationResult;

    if (refundPolicy.refund_percent <= 0) {
      // await this.emailService.sendCancellationEmail(reservation.bookerDetails.email, reservation, property);
      ResponseUtil.success(res, 'Refund information retrieved', {
        refundableAmount: 0,
        refundablePercentage: 0,
        description: refundPolicy.description,
      });
      return;
    }

    const payment = await validatePayment(reservationId, res);
    if (!payment) return;

    if (paymentProvider === PaymentProviderEnum.STRIPE) {
      await this.processStripeRefund(payment, refundableAmount, reservationId, res, cancellationReason);
    }
  });

  async processStripeRefund(
    payment: IPayment,
    refundableAmount: number,
    reservationId: string,
    res: IResponse,
    cancellationReason?: string,
  ): Promise<boolean> {
    const sessionId = payment.paymentGatewayResponse?.sessionId;
    if (!sessionId) {
      ResponseUtil.badRequest(res, 'No Payment Info Found');
      return false;
    }

    const session = await mongoose.startSession();
    try {
      let reservation: IReservation | null = null;

      await session.withTransaction(async () => {
        const refundData = await createStripeRefund(sessionId as string, refundableAmount);
        await PaymentModel.findByIdAndUpdate(
          payment._id,
          {
            status: PaymentStatusEnum.REFUNDED,
            refundResponse: { ...refundData, amount: refundableAmount },
          },
          { session },
        );

        reservation = await Reservation.findByIdAndUpdate(
          reservationId,
          {
            $set: {
              paymentStatus: PaymentStatusEnum.REFUNDED,
              status: ReservationStatusEnum.CANCELLED,
              refundAmount: refundableAmount,
              cancellationReason: cancellationReason ?? 'Refunded',
            },
          },
          { new: true, session },
        );

        if (!reservation) {
          throw new Error('Reservation not found');
        }

        await ReservationItem.updateMany(
          { _id: { $in: reservation.items } },
          { status: ReservationStatusEnum.CANCELLED, paymentStatus: PaymentStatusEnum.REFUNDED },
          { session },
        );
      });

      if (!reservation) {
        ResponseUtil.notFound(res, 'Reservation not found');
        return false;
      }

      // await this.emailService.sendCancellationEmail(reservation.bookerDetails.email, reservation, property);

      ResponseUtil.success(res, 'Refund processed successfully', { reservation });
      return true;
    } catch (error) {
      console.error('Refund transaction failed:', error);
      ResponseUtil.internalServerError(res, error instanceof Error ? error.message : 'Refund failed');
      return false;
    } finally {
      await session.endSession();
    }
  }
}
