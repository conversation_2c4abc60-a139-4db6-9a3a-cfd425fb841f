import { IRequest, IResponse } from '../types';
import { getImageUrl, uploadImage } from '../services/s3.service';
import { ResponseUtil } from '../utils/response';
import { asyncRequestHandler } from '../middlewares/async-request-handler';

export class ImageController {
  private static Instance: ImageController;

  public static getInstance(): ImageController {
    if (!ImageController.Instance) ImageController.Instance = new ImageController();
    return ImageController.Instance;
  }

  private constructor() {}

  upload = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    if (!req.file) {
      return ResponseUtil.badRequest(res, 'No file uploaded');
    }
    const key = await uploadImage(req.file);
    ResponseUtil.success(res, 'Image uploaded successfully', { key });
  });

  preview = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const url = await getImageUrl(req.params.key);
    ResponseUtil.success(res, 'Image URL retrieved successfully', { url });
  });
}
