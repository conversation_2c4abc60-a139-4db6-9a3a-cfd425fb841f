import mongoose, { Schema } from 'mongoose';
import { BaseSchema } from './base.model';
import { GenderEnum, IReservation, PaymentStatusEnum, ReservationStatusEnum } from '../types';
import { phoneSchema } from './property.model';

const ReservationSchema: Schema = new Schema<IReservation>({
  reservationCode: { type: String, required: true },
  invoiceCode: { type: String, required: true },
  propertyId: {
    type: Schema.Types.ObjectId,
    ref: 'Property',
    required: true,
  },
  items: [
    {
      type: Schema.Types.ObjectId,
      ref: 'ReservationItem',
    },
  ],
  subTotal: { type: Number, required: true },
  totalTax: { type: Number, required: true },
  grandTotal: { type: Number, required: true },
  status: {
    type: String,
    enum: Object.values(ReservationStatusEnum),
    default: ReservationStatusEnum.PENDING,
    required: true,
  },
  paymentStatus: {
    type: String,
    enum: Object.values(PaymentStatusEnum),
    default: PaymentStatusEnum.PENDING,
    required: true,
  },
  bookerDetails: {
    firstName: { type: String, required: true },
    lastName: { type: String, required: true },
    email: { type: String, required: true },
    phone: phoneSchema,
    gender: {
      type: String,
      enum: Object.values(GenderEnum),
      required: true,
      default: GenderEnum.OTHER,
    },
  },
  refundAmount: { type: Number, required: false },
  cancellationReason: { type: String, required: false },
});

ReservationSchema.add(BaseSchema);

ReservationSchema.index({ reservationCode: 1 }, { unique: true });
ReservationSchema.index({ invoiceCode: 1 }, { unique: true });
ReservationSchema.index({ propertyId: 1, status: 1, paymentStatus: 1 });

export default mongoose.model<IReservation>('Reservation', ReservationSchema, 'Reservation');
