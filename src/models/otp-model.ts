import mongoose, { Schema } from 'mongoose';
import { BaseSchema } from './base.model';
import { IOtp, OtpPurposeEnum } from '../types';

const OTPSchema: Schema = new Schema<IOtp>({
  userEmail: {
    type: String,
    required: true,
    index: true,
  },
  reservationCode: {
    type: String,
    required: false,
  },
  purpose: {
    type: String,
    required: true,
    enum: Object.values(OtpPurposeEnum),
  },
  otp: {
    type: String,
    required: true,
  },
  expiresAt: {
    type: Date,
    required: true,
    index: { expires: '10m' },
  },
});

OTPSchema.add(BaseSchema);

export default mongoose.model<IOtp>('OTP', OTPSchema, 'OTP');
