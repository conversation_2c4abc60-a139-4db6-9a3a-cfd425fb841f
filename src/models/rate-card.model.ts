import mongoose, { Schema } from 'mongoose';
import { BaseSchema } from './base.model';
import { IRateCard } from '../types';

const RateCardSchema: Schema = new Schema<IRateCard>({
  name: { type: String, required: true },
  description: { type: String, required: false },
  propertyId: {
    type: Schema.Types.ObjectId,
    required: true,
    ref: 'Property',
  },
  dateTime: { type: Date, required: true },
  packageId: { type: Schema.Types.ObjectId, required: true, ref: 'Package' },
  price: { type: Number, required: true },
});

RateCardSchema.add(BaseSchema);

RateCardSchema.index({ propertyId: 1, packageId: 1, dateTime: 1 }, { unique: true });

export default mongoose.model<IRateCard>('RateCard', RateCardSchema, 'RateCard');
