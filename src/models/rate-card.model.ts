import mongoose, { Schema } from 'mongoose';
import { BaseSchema } from './base.model';
import { IRateCard } from '../types';

const RateCardSchema: Schema = new Schema<IRateCard>({
  // Legacy fields (keeping for backward compatibility)
  name: { type: String, required: false },
  description: { type: String, required: false },
  propertyId: {
    type: Schema.Types.ObjectId,
    required: true,
    ref: 'Property',
  },
  dateTime: { type: Date, required: false }, // Optional for rule-based system
  packageId: { type: Schema.Types.ObjectId, required: false, ref: 'Package' }, // Optional for rule-based system
  price: { type: Number, required: false }, // Optional for rule-based system

  // New rule-based fields
  unitId: {
    type: Schema.Types.ObjectId,
    required: false,
    ref: 'UnitType',
  },
  fromDate: { type: Date, required: false },
  toDate: { type: Date, required: false },
  days: {
    type: [String],
    required: false,
    validate: {
      validator: function (days: string[]) {
        if (!days || days.length === 0) return true; // Allow empty for legacy records
        const validDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday', 'all'];
        return days.every(day => validDays.includes(day.toLowerCase()));
      },
      message: 'Invalid day specified. Valid days are: monday, tuesday, wednesday, thursday, friday, saturday, sunday, all'
    }
  },
  fromTime: {
    type: String,
    required: false,
    default: '00:00',
    validate: {
      validator: function (time: string) {
        if (!time) return true; // Allow empty for legacy records
        return /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(time);
      },
      message: 'Time must be in HH:MM format (24-hour)'
    }
  },
  toTime: {
    type: String,
    required: false,
    default: '23:59',
    validate: {
      validator: function (time: string) {
        if (!time) return true; // Allow empty for legacy records
        return /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(time);
      },
      message: 'Time must be in HH:MM format (24-hour)'
    }
  },
  percentageChange: {
    type: Number,
    required: false,
    validate: {
      validator: function (percentage: number) {
        if (percentage === undefined || percentage === null) return true; // Allow empty for legacy records
        return percentage >= -100; // Cannot reduce price by more than 100%
      },
      message: 'Percentage change cannot be less than -100%'
    }
  },
  isActive: { type: Boolean, required: false, default: true },
  version: { type: Number, required: false, default: 1 },
  archivedAt: { type: Date, required: false },
  archivedReason: { type: String, required: false }
});

RateCardSchema.add(BaseSchema);

// Validation for rule-based system
RateCardSchema.pre('save', function (next) {
  // If it's a rule-based rate card, validate required fields
  if (this.unitId && this.fromDate && this.toDate) {
    if (this.fromDate > this.toDate) {
      next(new Error('fromDate must be less than or equal to toDate'));
      return;
    }

    // Validate time range if not full day
    if (this.fromTime && this.toTime && (this.fromTime !== '00:00' || this.toTime !== '23:59')) {
      const fromMinutes = this.fromTime.split(':').reduce((acc, time) => (60 * acc) + +time, 0);
      const toMinutes = this.toTime.split(':').reduce((acc, time) => (60 * acc) + +time, 0);

      if (fromMinutes >= toMinutes) {
        next(new Error('fromTime must be less than toTime'));
        return;
      }
    }
  }

  // Legacy validation - if it's a legacy rate card, ensure required fields
  if (!this.unitId && (!this.packageId || !this.dateTime || this.price === undefined)) {
    next(new Error('Legacy rate cards require packageId, dateTime, and price'));
    return;
  }

  next();
});

// Indexes for both legacy and new system
RateCardSchema.index({ propertyId: 1, packageId: 1, dateTime: 1 }); // Legacy index (removed unique constraint)
RateCardSchema.index({ unitId: 1, fromDate: 1, toDate: 1, fromTime: 1, toTime: 1 }); // New rule-based index
RateCardSchema.index({ unitId: 1, isActive: 1, createdAt: -1 }); // For active rules query
RateCardSchema.index({ fromDate: 1, toDate: 1, days: 1 }); // For date range queries

export default mongoose.model<IRateCard>('RateCard', RateCardSchema, 'RateCard');
