import mongoose, { Schema } from 'mongoose';
import { BaseSchema } from './base.model';
import { IRateCard } from '../types';

const RateCardSchema: Schema = new Schema<IRateCard>({
  propertyId: {
    type: Schema.Types.ObjectId,
    required: true,
    ref: 'Property',
  },
  packageId: {
    type: Schema.Types.ObjectId,
    required: true,
    ref: 'Package',
  },
  fromDate: { type: Date, required: true },
  toDate: { type: Date, required: true },
  days: {
    type: [String],
    required: true,
    validate: {
      validator: function (days: string[]) {
        const validDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday', 'all'];
        return days.every((day) => validDays.includes(day.toLowerCase()));
      },
      message:
        'Invalid day specified. Valid days are: monday, tuesday, wednesday, thursday, friday, saturday, sunday, all',
    },
  },
  fromTime: {
    type: String,
    required: true,
    default: '00:00',
    validate: {
      validator: function (time: string) {
        return /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(time);
      },
      message: 'Time must be in HH:MM format (24-hour)',
    },
  },
  toTime: {
    type: String,
    required: true,
    default: '23:59',
    validate: {
      validator: function (time: string) {
        return /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(time);
      },
      message: 'Time must be in HH:MM format (24-hour)',
    },
  },
  percentageChange: {
    type: Number,
    required: true,
    validate: {
      validator: function (percentage: number) {
        return percentage >= -100;
      },
      message: 'Percentage change cannot be less than -100%',
    },
  },
});

RateCardSchema.add(BaseSchema);

// Indexes for rule-based system
RateCardSchema.index({ packageId: 1, fromDate: 1, toDate: 1, fromTime: 1, toTime: 1 });
RateCardSchema.index({ packageId: 1, isActive: 1, createdAt: -1 });
RateCardSchema.index({ propertyId: 1, isActive: 1, fromDate: 1 });
RateCardSchema.index({ fromDate: 1, toDate: 1, days: 1 });

export default mongoose.model<IRateCard>('RateCard', RateCardSchema, 'RateCard');
