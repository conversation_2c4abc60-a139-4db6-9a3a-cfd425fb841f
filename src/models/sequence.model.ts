import mongoose, { Schema } from 'mongoose';
import { BaseSchema } from './base.model';
import { ISequence } from '../types';

const SequenceSchema = new Schema<ISequence>({
  name: { type: String, required: true },
  sequence_value: { type: Number, default: 0 },
  propertyId: { type: Schema.Types.ObjectId, ref: 'Property', required: true },
});

SequenceSchema.add(BaseSchema);

export default mongoose.model<ISequence>('Sequence', SequenceSchema, 'Sequence');
