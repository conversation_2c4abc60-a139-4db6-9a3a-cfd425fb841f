import mongoose, { Schema } from 'mongoose';
import { BaseSchema } from './base.model';
import { IUnitType } from '../types';

const UnitTypeSchema: Schema = new Schema<IUnitType>({
  code: { type: String, required: true, unique: true },
  name: { type: String, required: true },
  description: { type: String, required: false },
  totalUnits: { type: Number, required: true, default: 1 },
  capacity: { type: Number, required: true },
  area: { type: Number, required: false },
  bedType: { type: String, required: false },
  attachments: { type: [String], required: true },
  serviceTypeId: {
    type: Schema.Types.ObjectId,
    ref: 'DomainValue',
    required: true,
  },
  propertyId: {
    type: Schema.Types.ObjectId,
    ref: 'Property',
    required: true,
  },
  bufferTime: { type: Number, required: true, default: 0 },

  bathroomType: { type: String, required: false },
  isSmokingAllowed: { type: Boolean, required: false },
});

UnitTypeSchema.index({ code: 1, propertyId: 1 }, { unique: true });
UnitTypeSchema.index({ name: 1, propertyId: 1 }, { unique: true });

UnitTypeSchema.add(BaseSchema);

export default mongoose.model<IUnitType>('UnitType', UnitTypeSchema, 'UnitType');
