import mongoose, { Schema } from 'mongoose';
import { BaseSchema } from './base.model';
import { IRateCardRule } from '../types';

const RateCardRuleSchema: Schema = new Schema<IRateCardRule>({
  unitId: {
    type: Schema.Types.ObjectId,
    required: true,
    ref: 'UnitType',
  },
  fromDate: { type: Date, required: true },
  toDate: { type: Date, required: true },
  days: {
    type: [String],
    required: true,
    validate: {
      validator: function (days: string[]) {
        const validDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday', 'all'];
        return days.every(day => validDays.includes(day.toLowerCase()));
      },
      message: 'Invalid day specified. Valid days are: monday, tuesday, wednesday, thursday, friday, saturday, sunday, all'
    }
  },
  fromTime: { 
    type: String, 
    required: true, 
    default: '00:00',
    validate: {
      validator: function (time: string) {
        return /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(time);
      },
      message: 'Time must be in HH:MM format (24-hour)'
    }
  },
  toTime: { 
    type: String, 
    required: true, 
    default: '23:59',
    validate: {
      validator: function (time: string) {
        return /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(time);
      },
      message: 'Time must be in HH:MM format (24-hour)'
    }
  },
  percentageChange: { 
    type: Number, 
    required: true,
    validate: {
      validator: function (percentage: number) {
        return percentage >= -100; // Cannot reduce price by more than 100%
      },
      message: 'Percentage change cannot be less than -100%'
    }
  },
  isActive: { type: Boolean, required: true, default: true },
  version: { type: Number, required: true, default: 1 },
  archivedAt: { type: Date, required: false },
  archivedReason: { type: String, required: false }
});

RateCardRuleSchema.add(BaseSchema);

// Validation to ensure fromDate <= toDate
RateCardRuleSchema.pre('save', function (next) {
  if (this.fromDate > this.toDate) {
    next(new Error('fromDate must be less than or equal to toDate'));
  } else {
    next();
  }
});

// Validation to ensure fromTime < toTime (unless it's a full day rule)
RateCardRuleSchema.pre('save', function (next) {
  if (this.fromTime !== '00:00' || this.toTime !== '23:59') {
    const fromMinutes = this.fromTime.split(':').reduce((acc, time) => (60 * acc) + +time, 0);
    const toMinutes = this.toTime.split(':').reduce((acc, time) => (60 * acc) + +time, 0);
    
    if (fromMinutes >= toMinutes) {
      next(new Error('fromTime must be less than toTime'));
    } else {
      next();
    }
  } else {
    next();
  }
});

// Indexes for performance optimization
RateCardRuleSchema.index({ unitId: 1, fromDate: 1, toDate: 1, fromTime: 1, toTime: 1 });
RateCardRuleSchema.index({ unitId: 1, isActive: 1, createdAt: -1 });
RateCardRuleSchema.index({ fromDate: 1, toDate: 1, days: 1 });

export default mongoose.model<IRateCardRule>('RateCardRule', RateCardRuleSchema, 'RateCardRule');
