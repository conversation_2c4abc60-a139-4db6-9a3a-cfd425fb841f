import mongoose, { Schema } from 'mongoose';
import { BaseSchema } from './base.model';
import { UserTokenDocument } from '../types';

export const userTokenDocument: Schema = new Schema<UserTokenDocument>({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  token: { type: String, required: true },
});

userTokenDocument.add(BaseSchema);

export default mongoose.model<UserTokenDocument>('UserToken', userTokenDocument, 'UserToken');
