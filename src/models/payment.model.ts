import mongoose, { Schema, Types } from 'mongoose';
import Sequence from './sequence.model';
import { BaseSchema } from './base.model';
import { IPayment, PaymentMethod, PaymentStatusEnum, PaymentType } from '../types';

const paymentSchema: Schema = new Schema<IPayment>({
  reservationId: { type: Types.ObjectId, required: true, ref: 'Reservation' },
  propertyId: { type: Types.ObjectId, required: true, ref: 'Property' },
  amount: { type: Number, required: true },
  currency: { type: String, required: true, default: 'INR' },
  status: {
    type: String,
    required: true,
    enum: PaymentStatusEnum,
    default: PaymentStatusEnum.PENDING,
  },
  paymentMethod: {
    type: String,
    required: true,
    enum: PaymentMethod,
    default: PaymentMethod.CARD,
  },
  paymentType: {
    type: String,
    required: true,
    enum: PaymentType,
    default: PaymentType.ONLINE,
  },
  paymentGateway: {
    type: String,
    required: false,
    enum: ['stripe', 'paytm', 'razorpay', 'paypal', 'other'],
    default: 'stripe',
  },
  paymentGatewayResponse: { type: Schema.Types.Mixed, required: false },
  refundResponse: { type: Schema.Types.Mixed, required: false },
});

paymentSchema.add(BaseSchema);

paymentSchema.pre<IPayment>('save', async function (next) {
  try {
    if (this.isNew) {
      const sequence = await Sequence.findOneAndUpdate(
        { name: 'paymentCode' },
        { $inc: { sequence_value: 1 } },
        { new: true, upsert: true, setDefaultsOnInsert: true },
      );

      if (!sequence) throw new Error('Failed to generate sequence number');

      this.code = sequence.sequence_value.toString().padStart(4, '0');
    }
    next();
  } catch (err) {
    next(err instanceof Error ? err : new Error(String(err)));
  }
});

export default mongoose.model<IPayment>('Payment', paymentSchema, 'Payment');
