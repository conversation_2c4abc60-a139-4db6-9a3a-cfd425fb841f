import mongoose, { Schema } from 'mongoose';
import { BaseSchema } from './base.model';
import { IPackage } from '../types';

const PackageSchema: Schema = new Schema<IPackage>({
  code: { type: String, required: false },
  name: { type: String, required: true },
  description: { type: String, required: false, default: '' },
  duration: { type: Number, required: true },
  propertyId: {
    type: Schema.Types.ObjectId,
    ref: 'Property',
    required: true,
  },
  unitTypeId: {
    type: Schema.Types.ObjectId,
    ref: 'UnitType',
    required: true,
  },
  price: { type: Number, required: true },
  taxes: {
    type: [Schema.Types.ObjectId],
    ref: 'DomainValue',
    required: true,
  },
  amenities: {
    type: [Schema.Types.ObjectId],
    ref: 'Amenities',
    required: false,
    default: [],
  },
  extraBed: {
    available: { type: Boolean, required: false },
    price: { type: Number, required: false },
  },
  noOfAdults: { type: Number, required: false },
  noOfChildren: { type: Number, required: false },
  gender: { type: String, required: false },
});

PackageSchema.add(BaseSchema);

PackageSchema.index({ code: 1, propertyId: 1 }, { unique: true });
PackageSchema.index({ name: 1, propertyId: 1 }, { unique: true });
PackageSchema.index({ propertyId: 1, duration: 1, noOfAdults: 1, noOfChildren: 1, gender: 1 });

export default mongoose.model<IPackage>('Package', PackageSchema, 'Package');
