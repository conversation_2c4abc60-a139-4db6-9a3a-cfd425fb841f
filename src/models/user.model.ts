import mongoose, { Schema } from 'mongoose';
import { addressSchema, phoneSchema } from './property.model';
import { BaseSchema } from './base.model';
import { IUser, UserRoleEnum } from '../types';

const UserSchema = new Schema<IUser>({
  firstName: { type: String, required: true },
  lastName: { type: String, required: true },
  email: { type: String, required: false, unique: true, sparse: true },
  userName: { type: String, required: true, unique: true },
  password: { type: String, required: false },
  phone: { type: phoneSchema, required: false },
  address: { type: addressSchema, required: false },
  role: {
    type: String,
    required: true,
    default: UserRoleEnum.USER,
    enum: Object.values(UserRoleEnum),
  },
});

UserSchema.path('email').validate(function (value) {
  return !(!value && !this.phone);
}, 'Either email or phone must be provided.');

UserSchema.path('phone').validate(function (value) {
  return !(!value && !this.email);
}, 'Either phone or email must be provided.');

UserSchema.add(BaseSchema);

UserSchema.index({ email: 1, phone: 1, userName: 1 }, { unique: true, sparse: true });

export default mongoose.model<IUser>('User', UserSchema, 'User');
