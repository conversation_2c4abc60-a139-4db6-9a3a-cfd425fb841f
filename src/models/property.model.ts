import mongoose, { Schema } from 'mongoose';
import Sequence from './sequence.model';
import { BaseSchema } from './base.model';
import { IAddress, IContact, IPhone, IProperty, PremisesTypeEnum, PropertyStatusEnum } from '../types';

export const phoneSchema = new Schema<IPhone>(
  {
    countryCode: { type: String, required: false, default: '+91' },
    phoneNumber: { type: String, required: false, default: '9999999999' },
  },
  { _id: false },
);

export const addressSchema = new Schema<IAddress>(
  {
    address1: { type: String, required: true },
    address2: { type: String, required: false },
    city: { type: String, required: true },
    state: { type: String, required: true },
    country: { type: String, required: true },
    zipcode: { type: String, required: false },
    latitude: { type: Number, required: false },
    longitude: { type: Number, required: false },
    locationId: {
      type: Schema.Types.ObjectId,
      required: false,
      ref: 'Location',
    },
    placeId: { type: String, required: false },
  },
  { _id: false },
);

export const contactSchema = new Schema<IContact>(
  {
    firstName: { type: String, required: true },
    lastName: { type: String, required: true },
    email: { type: String, required: true },
    phone: { type: phoneSchema, required: true },
  },
  { _id: false },
);

const PropertySchema: Schema = new Schema<IProperty>({
  active: { type: Boolean, required: true, default: false },
  serviceType: {
    type: Schema.Types.ObjectId,
    ref: 'DomainValue',
    required: true,
  },
  name: { type: String, required: true },
  description: { type: String, required: false },
  code: { type: String, required: false, unique: true },
  premisesType: {
    type: String,
    required: false,
    default: PremisesTypeEnum.AIRSIDE,
    enum: Object.values(PremisesTypeEnum),
  },
  website: { type: String },
  address: { type: addressSchema, required: true },
  owner: { type: Schema.Types.ObjectId, required: true, ref: 'User' },
  status: {
    type: String,
    required: true,
    default: PropertyStatusEnum.PENDING,
    enum: Object.values(PropertyStatusEnum),
  },
  buildYear: { type: Date, required: true },
  acceptBookingFrom: { type: Date, required: true },
});

PropertySchema.add(BaseSchema);

PropertySchema.index({ name: 1, owner: 1, serviceType: 1, 'address.locationId': 1 }, { unique: true });

PropertySchema.pre<IProperty>('save', async function (next) {
  try {
    if (this.isNew) {
      const sequence = await Sequence.findOneAndUpdate(
        { name: 'propertyCode' },
        { $inc: { sequence_value: 1 } },
        { new: true, upsert: true, setDefaultsOnInsert: true },
      );

      if (!sequence) throw new Error('Failed to generate sequence number');

      this.code = sequence.sequence_value.toString().padStart(4, '0');
    }
    next();
  } catch (err) {
    next(err instanceof Error ? err : new Error(String(err)));
  }
});

export default mongoose.model<IProperty>('Property', PropertySchema, 'Property');
