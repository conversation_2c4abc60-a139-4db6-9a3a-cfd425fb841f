import mongoose, { Schema } from 'mongoose';
import { BaseSchema } from './base.model';
import { ILocation } from '../types';

const LocationSchema: Schema = new Schema<ILocation>({
  code: { type: String, required: true, unique: true },
  name: { type: String, required: true },
  description: { type: String, required: false },
  latitude: { type: Number, required: true },
  longitude: { type: Number, required: true },
  city: { type: String, required: true },
  country: { type: String, required: true },
});

LocationSchema.add(BaseSchema);

export default mongoose.model<ILocation>('Location', LocationSchema, 'Location');
