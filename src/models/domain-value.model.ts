import mongoose, { Schema } from 'mongoose';
import { BaseSchema } from './base.model';
import { DomainValueLevel, IDomainValue } from '../types';

const DomainValueSchema: Schema = new Schema<IDomainValue>({
  propertyId: {
    type: Schema.Types.ObjectId,
    required: [
      function (this: IDomainValue) {
        return [DomainValueLevel.PROPERTY_DATA].includes(this.level as DomainValueLevel);
      },
      'propertyId is required for PROPERTY_DATA levels',
    ],
    ref: 'Property',
  },
  serviceTypeId: {
    type: Schema.Types.ObjectId,
    required: [
      function (this: IDomainValue) {
        return [DomainValueLevel.PROPERTY_META].includes(this.level as DomainValueLevel);
      },
      'serviceTypeId is required for PROPERTY_META levels',
    ],
    ref: 'DomainValue',
  },
  category: {
    type: String,
    required: [
      function (this: IDomainValue) {
        return [DomainValueLevel.PROPERTY_META].includes(this.level as DomainValueLevel);
      },
      'category is required for PROPERTY_META levels',
    ],
  },
  level: { type: String, required: true, enum: Object.values(DomainValueLevel) },
  name: { type: String, required: true },
  description: { type: String, required: false },
  value: { type: String, required: false, default: '' },
  displayOrder: { type: Number, required: false },
  icon: { type: String, required: false },
  parentId: { type: Schema.Types.ObjectId, required: false, ref: 'DomainValue' },
});

DomainValueSchema.add(BaseSchema);

DomainValueSchema.index(
  { propertyId: 1, categoryId: 1, level: 1, parentId: 1, name: 1 },
  {
    unique: true,
    partialFilterExpression: {
      propertyId: { $exists: true },
    },
  },
);

export default mongoose.model<IDomainValue>('DomainValue', DomainValueSchema, 'DomainValue');
