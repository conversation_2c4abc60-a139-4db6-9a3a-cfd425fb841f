import mongoose, { Schema } from 'mongoose';
import { BaseSchema } from './base.model';
import { IUnitAvailability } from '../types';

const UnitAvailabilitySchema: Schema = new Schema<IUnitAvailability>({
  // Legacy fields (keeping for backward compatibility)
  propertyId: {
    type: Schema.Types.ObjectId,
    required: true,
    ref: 'Property',
  },
  unitTypeId: { type: Schema.Types.ObjectId, required: true, ref: 'UnitType' },
  dateTime: { type: Date, required: false }, // Optional for rule-based system
  availability: { type: Number, required: false }, // Optional for rule-based system

  // New rule-based fields
  fromDate: { type: Date, required: false },
  toDate: { type: Date, required: false },
  days: {
    type: [String],
    required: false,
    validate: {
      validator: function (days: string[]) {
        if (!days || days.length === 0) return true; // Allow empty for legacy records
        const validDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday', 'all'];
        return days.every(day => validDays.includes(day.toLowerCase()));
      },
      message: 'Invalid day specified. Valid days are: monday, tuesday, wednesday, thursday, friday, saturday, sunday, all'
    }
  },
  fromTime: {
    type: String,
    required: false,
    default: '00:00',
    validate: {
      validator: function (time: string) {
        if (!time) return true; // Allow empty for legacy records
        return /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(time);
      },
      message: 'Time must be in HH:MM format (24-hour)'
    }
  },
  toTime: {
    type: String,
    required: false,
    default: '23:59',
    validate: {
      validator: function (time: string) {
        if (!time) return true; // Allow empty for legacy records
        return /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(time);
      },
      message: 'Time must be in HH:MM format (24-hour)'
    }
  },
  quantityAvailable: {
    type: Number,
    required: false,
    min: [0, 'Quantity available cannot be negative']
  },
  reason: { type: String, required: false },
  isActive: { type: Boolean, required: false, default: true },
  version: { type: Number, required: false, default: 1 },
  archivedAt: { type: Date, required: false },
  archivedReason: { type: String, required: false }
});

UnitAvailabilitySchema.add(BaseSchema);

// Validation for rule-based system
UnitAvailabilitySchema.pre('save', function (next) {
  // If it's a rule-based availability, validate required fields
  if (this.fromDate && this.toDate) {
    if (this.fromDate > this.toDate) {
      next(new Error('fromDate must be less than or equal to toDate'));
      return;
    }

    // Validate time range if not full day
    if (this.fromTime && this.toTime && (this.fromTime !== '00:00' || this.toTime !== '23:59')) {
      const fromMinutes = this.fromTime.split(':').reduce((acc, time) => (60 * acc) + +time, 0);
      const toMinutes = this.toTime.split(':').reduce((acc, time) => (60 * acc) + +time, 0);

      if (fromMinutes >= toMinutes) {
        next(new Error('fromTime must be less than toTime'));
        return;
      }
    }
  }

  // Legacy validation - if it's a legacy availability, ensure required fields
  if (!this.fromDate && (!this.dateTime || this.availability === undefined)) {
    next(new Error('Legacy availability records require dateTime and availability'));
    return;
  }

  next();
});

// Validation to ensure quantityAvailable doesn't exceed unit type's total units
UnitAvailabilitySchema.pre('save', async function (next) {
  try {
    if (this.quantityAvailable !== undefined) {
      const UnitType = mongoose.model('UnitType');
      const unitType = await UnitType.findById(this.unitTypeId);

      if (unitType && this.quantityAvailable > unitType.totalUnits) {
        next(new Error(`Quantity available (${this.quantityAvailable}) cannot exceed total units (${unitType.totalUnits})`));
        return;
      }
    }
    next();
  } catch (error) {
    next(error instanceof Error ? error : new Error(String(error)));
  }
});

// Indexes for both legacy and new system
UnitAvailabilitySchema.index({ propertyId: 1, unitTypeId: 1, dateTime: 1 }); // Legacy index (removed unique constraint)
UnitAvailabilitySchema.index({ unitTypeId: 1, fromDate: 1, toDate: 1, fromTime: 1, toTime: 1 }); // New rule-based index
UnitAvailabilitySchema.index({ unitTypeId: 1, isActive: 1, createdAt: -1 }); // For active rules query
UnitAvailabilitySchema.index({ fromDate: 1, toDate: 1, days: 1 }); // For date range queries

export default mongoose.model<IUnitAvailability>(
  'UnitTypeAvailability',
  UnitAvailabilitySchema,
  'UnitTypeAvailability',
);
