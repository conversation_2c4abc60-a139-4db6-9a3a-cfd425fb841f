import mongoose, { Schema } from 'mongoose';
import { BaseSchema } from './base.model';
import { IUnitAvailability } from '../types';

const UnitAvailabilitySchema: Schema = new Schema<IUnitAvailability>({
  propertyId: {
    type: Schema.Types.ObjectId,
    required: true,
    ref: 'Property',
  },
  unitTypeId: { type: Schema.Types.ObjectId, required: true, ref: 'UnitType' },
  fromDate: { type: Date, required: true },
  toDate: { type: Date, required: true },
  days: {
    type: [String],
    required: true,
    validate: {
      validator: function (days: string[]) {
        const validDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday', 'all'];
        return days.every((day) => validDays.includes(day.toLowerCase()));
      },
      message:
        'Invalid day specified. Valid days are: monday, tuesday, wednesday, thursday, friday, saturday, sunday, all',
    },
  },
  fromTime: {
    type: String,
    required: true,
    default: '00:00',
    validate: {
      validator: function (time: string) {
        return /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(time);
      },
      message: 'Time must be in HH:MM format (24-hour)',
    },
  },
  toTime: {
    type: String,
    required: true,
    default: '23:59',
    validate: {
      validator: function (time: string) {
        return /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(time);
      },
      message: 'Time must be in HH:MM format (24-hour)',
    },
  },
  quantityAvailable: {
    type: Number,
    required: true,
    min: [0, 'Quantity available cannot be negative'],
  },
  reason: { type: String, required: false },
  isActive: { type: Boolean, required: true, default: true },
  version: { type: Number, required: true, default: 1 },
  archivedAt: { type: Date, required: false },
  archivedReason: { type: String, required: false },
});

UnitAvailabilitySchema.add(BaseSchema);

UnitAvailabilitySchema.index({ unitTypeId: 1, fromDate: 1, toDate: 1, fromTime: 1, toTime: 1 });
UnitAvailabilitySchema.index({ unitTypeId: 1, isActive: 1, createdAt: -1 });
UnitAvailabilitySchema.index({ propertyId: 1, isActive: 1, fromDate: 1 });
UnitAvailabilitySchema.index({ fromDate: 1, toDate: 1, days: 1 });

export default mongoose.model<IUnitAvailability>(
  'UnitTypeAvailability',
  UnitAvailabilitySchema,
  'UnitTypeAvailability',
);
