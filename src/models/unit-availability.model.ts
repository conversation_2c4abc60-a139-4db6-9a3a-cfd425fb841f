import mongoose, { Schema } from 'mongoose';
import { BaseSchema } from './base.model';
import { IUnitAvailability } from '../types';

const UnitAvailabilitySchema: Schema = new Schema<IUnitAvailability>({
  propertyId: {
    type: Schema.Types.ObjectId,
    required: true,
    ref: 'Property',
  },
  dateTime: { type: Date, required: true },
  unitTypeId: { type: Schema.Types.ObjectId, required: true, ref: 'UnitType' },
  availability: { type: Number, required: true },
});

UnitAvailabilitySchema.add(BaseSchema);

UnitAvailabilitySchema.index({ propertyId: 1, unitTypeId: 1, dateTime: 1 }, { unique: true });

export default mongoose.model<IUnitAvailability>(
  'UnitTypeAvailability',
  UnitAvailabilitySchema,
  'UnitTypeAvailability',
);
