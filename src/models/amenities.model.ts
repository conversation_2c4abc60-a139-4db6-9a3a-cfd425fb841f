import mongoose, { Schema } from 'mongoose';
import { BaseSchema } from './base.model';
import { IAmenity, IIcon } from '../types';

const IconModel = new Schema<IIcon>(
  {
    web: { type: String, required: false },
    mobile: { type: String, required: false },
  },
  { _id: false },
);

const AmenityModel = new Schema<IAmenity>({
  name: { type: String, required: true },
  description: { type: String, required: false },
  icon: IconModel,
  categoryId: { type: Schema.Types.ObjectId, ref: 'DomainValue', required: true },
  serviceTypeId: { type: Schema.Types.ObjectId, ref: 'DomainValue', required: true },
});

AmenityModel.index({ name: 1, categoryId: 1, serviceTypeId: 1 }, { unique: true });

AmenityModel.add(BaseSchema);

export default mongoose.model<IAmenity>('Amenities', AmenityModel, 'Amenities');
