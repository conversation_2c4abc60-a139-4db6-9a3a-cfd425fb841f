import mongoose, { Schema } from 'mongoose';
import { BaseSchema } from './base.model';
import { ICompletedPayouts, PaymentModeEnum } from '../types';

const CompletedPayoutSchema: Schema = new Schema<ICompletedPayouts>({
  propertyId: {
    type: Schema.Types.ObjectId,
    required: true,
    ref: 'Property',
  },
  netPayout: { type: Number, required: true },
  date: { type: Date, required: true },
  payoutUntil: { type: Date, required: true },
  mode: {
    type: String,
    required: true,
    enum: Object.values(PaymentModeEnum),
  },
  referenceNumber: { type: String, required: true },
  attachment: { type: [String], required: false },
});

CompletedPayoutSchema.add(BaseSchema);

export default mongoose.model<ICompletedPayouts>('CompletedPayouts', CompletedPayoutSchema, 'CompletedPayouts');
