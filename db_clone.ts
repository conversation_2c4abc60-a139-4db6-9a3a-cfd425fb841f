import mongoose from 'mongoose';
import { promises as fs } from 'fs';
import path from 'path';
import { MongoClient } from 'mongodb';

// 🔹 Config: replace with actual values or env vars
const SOURCE_URI = 'mongodb+srv://hrmsuser:<EMAIL>/hrms-prod?retryWrites=true&w=majority&ssl=true';
const TARGET_URI = 'mongodb+srv://hrmsuser:<EMAIL>/hrms-qa?retryWrites=true&w=majority&appName=ST-Dev';
const BACKUP_DIR = path.resolve('./db-backups');
const BACKUP_FILE = path.join(BACKUP_DIR, 'target-backup.json');

async function backupTargetDB() {
  console.log('📦 Backing up target database...');
  const targetClient = new MongoClient(TARGET_URI);
  await targetClient.connect();
  const targetDb = targetClient.db();

  const collections = await targetDb.collections();
  const backup: Record<string, any[]> = {};

  for (const col of collections) {
    const data = await col.find({}).toArray();
    backup[col.collectionName] = data;
  }

  await fs.mkdir(BACKUP_DIR, { recursive: true });
  await fs.writeFile(BACKUP_FILE, JSON.stringify(backup, null, 2));

  await targetClient.close();
  console.log(`✅ Target DB backup saved to ${BACKUP_FILE}`);
}

async function clearTargetDB() {
  console.log('🧹 Clearing target database...');
  const targetClient = new MongoClient(TARGET_URI);
  await targetClient.connect();
  const targetDb = targetClient.db();

  const collections = await targetDb.collections();
  for (const col of collections) {
    await col.drop();
    console.log(`🗑 Dropped collection: ${col.collectionName}`);
  }

  await targetClient.close();
  console.log('✅ Target database cleared.');
}

async function cloneDB() {
  console.log('🔄 Cloning source DB to target DB...');
  const sourceClient = new MongoClient(SOURCE_URI);
  const targetClient = new MongoClient(TARGET_URI);

  await sourceClient.connect();
  await targetClient.connect();

  const sourceDb = sourceClient.db();
  const targetDb = targetClient.db();

  const collections = await sourceDb.listCollections().toArray();

  for (const { name } of collections) {
    const sourceCol = sourceDb.collection(name);
    const targetCol = targetDb.collection(name);

    // Copy indexes
    const indexes = await sourceCol.indexes();
    for (const idx of indexes) {
      const { key, name: idxName, unique, sparse, background } = idx;
      try {
        await targetCol.createIndex(key, { name: idxName, unique, sparse, background });
      } catch (e) {
        console.warn(`⚠️ Could not create index ${idxName} on ${name}:`, e);
      }
    }

    // Copy data
    const docs = await sourceCol.find({}).toArray();
    if (docs.length > 0) {
      await targetCol.insertMany(docs);
    }

    console.log(`✅ Cloned ${docs.length} docs from ${name}`);
  }

  await sourceClient.close();
  await targetClient.close();
  console.log('🎉 Database clone completed successfully!');
}

async function main() {
  try {
    await backupTargetDB();
    await clearTargetDB();
    await cloneDB();
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
  }
}

main();
