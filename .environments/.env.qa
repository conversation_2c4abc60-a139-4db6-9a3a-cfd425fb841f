MONGO_URI=mongodb+srv://admin:<EMAIL>/Stay-Transit-QA?retryWrites=true&w=majority&ssl=true
PORT=3010

ACCESS_TOKEN_SECRET=aWfampNpNAsSaTUJkMklKFzQxRlhXXgvTnGWMWPQALxdpxiHYNMUuYYwMuDSzgWs
REFRESH_TOKEN_SECRET=uOprIsMYRHgGbprQjruPcsHtxxlaGfpkUTPMFJutwLeCdpVGpAuioIEdGotIZUWL
MISC_TOKEN_SECRET=sylFGyuOKqprrXdrvIcFbeDURMnfJZZmadQUmaWzvLBraxQamEDBkUsFAkldIvJY

MASTER_COMPANY_ID=6087ba5f4c5778bbb4085523

SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASSWORD=gbsg axqm ffpb qgos
SMTP_FROM=<EMAIL>
ERROR_EMAIL_RECIPIENT=<EMAIL>

AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=403ZgbkE4nQTJAnuzbmjv/CHPvO9ynTFSyBwmn+j
AWS_REGION=us-east-1
AWS_S3_BUCKET=elaachi-master-files


STRIPE_SECRET_KEY=sk_test_51KYjskCmnzrHbnSWeFFlorOrkfRk1jyxy5UixHE5COoKaH8fHeE4BOmyiNFNlU8jkIDslBT0NgZewuwzLrk5BVIx00BHTwCoht
GUEST_FRONTEND_URL=http://localhost:5173
DASHBOARD_FRONTEND_URL=http://localhost:5173

GOOGLE_MAPS_API_KEY=AIzaSyBP9y2eh_Ow5GCaUU3RRinKYjSLnfue1rE

# Firebase
FIREBASE_TYPE=service_account
FIREBASE_PROJECT_ID=staytransit-3b4ab
FIREBASE_PRIVATE_KEY_ID=3a72dafcf9ce345f82f0220539e551eed5a723ae
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=111751132374910724015
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
FIREBASE_AUTH_PROVIDER_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
FIREBASE_CLIENT_X509_CERT_URL=https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-fbsvc%40staytransit-3b4ab.iam.gserviceaccount.com

# Allowed Origins
ALLOWED_ORIGINS=http://localhost:5173,http://localhost:5174,http://localhost:3000,https://demo.staytransit.com,https://admin.staytransit.com
ALLOWED_HEADERS=Content-Type,skip_zrok_interstitial,Authorization

# Node Environment
NODE_ENV=QA

# Rate Limit
WINDOW_MS = 15 # in Minutes
LIMIT = 100