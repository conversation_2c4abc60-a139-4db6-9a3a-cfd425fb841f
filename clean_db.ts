import { MongoClient } from 'mongodb';

// 🔹 Replace with your actual MongoDB URI and DB name
const DB_URI = 'mongodb+srv://admin:<EMAIL>/Stay-Transit-new?retryWrites=true&w=majority&ssl=true';

async function cleanDatabase() {
  const client = new MongoClient(DB_URI);
  try {
    await client.connect();
    const db = client.db();

    console.log(`🧹 Cleaning database: ${db.databaseName}`);
    const collections = await db.collections();

    if (collections.length === 0) {
      console.log('⚠️ No collections found, database already empty.');
    }

    for (const col of collections) {
      await col.drop();
      console.log(`🗑 Dropped collection: ${col.collectionName}`);
    }

    console.log(`✅ Database ${db.databaseName} is now empty.`);
  } catch (error) {
    console.error('❌ Error cleaning database:', error);
  } finally {
    await client.close();
  }
}

cleanDatabase();
