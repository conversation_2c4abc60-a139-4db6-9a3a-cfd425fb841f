import mongoose from 'mongoose';
import { IProperty, IPropertyDetails } from './src/types';
import PropertyDetailsModel from './src/models/property-details.model';
import PropertyModel from './src/models/property.model';
import DomainValueModel from './src/models/domain-value.model';

const MONGO_URI = 'mongodb+srv://st_db_user:<EMAIL>/Stay-Transit-Dev?retryWrites=true&w=majority&appName=ST-Dev';

const imageMap: Record<string, string[]> = {
  hotel: [
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/c3a4b2a4-ff88-44f0-ae78-3bc33345944e-129941a88b7311e7a7ab0a4cef95d023.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/a2d53d04-78fa-45b6-a89f-b6853236b779-gettyimages-1333257950-612x612.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/a2d53d04-78fa-45b6-a89f-b6853236b779-gettyimages-1333257950-612x612.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/18e70dc0-56b3-4132-b044-99052a1eecc2-Hotal%20entrance.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/27910961-ce4b-418e-a937-5cdbe4ebb60f-Hotal%20Pictures.jpeg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/17b8d43d-630a-4897-9b01-dc05599158e1-Hotal%20Reception2.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/e18679e7-f39b-4b6c-9a44-dc91878793cd-Hotal%20stay.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/2b645088-d45d-4dd3-a8a5-34f3f1824ee7-hotel-rooms-8146308.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/acc08c6e-68f8-4515-b53f-4aa8db65419a-Hotel.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/e39bd260-7e4c-4d5b-b991-ba1b39528b31-ihgor-member-rate-web-offers-1440x720.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/a11f818e-afb3-437b-a51b-c4e028d8fdbd-istockphoto-104731717-612x612.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/3a452578-928f-4147-88d9-799e34465ac4-istockphoto-1331465591-612x612.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/7eeec687-285d-4e7f-b105-b4fb1e2a7267-istockphoto-1418784247-612x612.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/ac06093d-9754-4a39-93d1-04cdce821fb1-lh_boutique_hotel_feat.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/3004a4bb-ea95-408c-a1ab-cae7b77a1844-LivingRoomReimagined-1920x1200.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/eb7808ef-de13-4efd-8b9f-aa968027a7ce-Lobby_B_op2_1_wfaceo.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/bfd97702-4b0a-4525-914c-ed408eec7a7b-Luxury%20hotal.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/394ab2c5-7fad-4a2b-b59a-505d2440c99e-Orchid%20hotal.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/ec422b5b-f58e-458a-ac20-c4a6a35f8242-pexels-pixabay-261102.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/24c527ae-2779-4f91-8401-72f68b08a11f-Pink-Pearl-Hotel-Reception-Area.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/33d8c85f-1911-4cbe-bff5-6a454869d5db-types-of-hotels-featured.png",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/abd82b8c-0832-4d1b-9152-94bd9ccb00cc-Vista-Exterior_Villa-Le-Blanc-e1655346748492.jpg",
  ],
  lounge: [
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/c895c9a5-07a5-4c42-b9be-dc5f593df967-3d-illustration-mockup-photo-frame-in-lounge-or-ca-2025-01-16-14-18-45-utc.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/1a9039b0-201d-4e53-930e-2a6ac623936b-3d-illustration-mockup-photo-frame-in-lounge-rende-2025-01-16-14-25-39-utc.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/9ec974e6-d64a-4b19-b51e-08d73ee93a40-cocktails-2025-04-01-20-38-06-utc.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/51684bb0-3d6b-447a-b784-9b904fa36ff9-contemporary-studio-apartments-in-wooden-colors-wi-2024-10-20-02-48-45-utc.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/0569a904-01f9-41dc-9738-07948ee4253c-dinner-in-a-cozy-restaurant-2025-01-09-21-55-11-utc.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/73f163dc-8bc3-44b5-96d3-d9ae5c578d2a-empty-airport-lounge-2024-12-02-11-44-51-utc.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/1b36f540-747c-4a6f-960e-d71b02773600-food-on-a-table-in-a-hotel-room-2024-10-18-23-51-17-utc.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/719bc12a-4b8a-4dcb-b6aa-d75ebca81964-fresh-and-delisious-breakfast-in-outdoor-cafe-dif-2025-02-12-01-44-39-utc.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/ada14b56-7c71-49ec-8567-7b7e6ab04118-hotel-2025-03-23-18-46-40-utc.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/40761e25-53d1-4c93-8b30-090b1effb215-hotel-breakfast-continental-style-served-on-a-tra-2024-12-02-18-51-55-utc.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/f66871fe-cb45-48fb-812e-e34842a94ac6-hotel-reception-lobby-2024-09-21-22-58-29-utc.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/c73e4cf4-1da3-4f4b-89d5-bb826c99f2e4-hotel-room-2024-09-17-18-09-46-utc.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/98c5e06d-07f6-4869-8b0b-2dddc6c2b161-interior-of-comfortable-restaurant-with-tables-and-2025-03-17-13-49-41-utc.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/ffc1af23-23eb-4654-b42e-89ed2c18239c-lobby-area-of-a-hotel-over-the-vintage-wall-backgr-2024-10-21-11-26-34-utc.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/46ddc5e9-ee2b-40d5-9d06-0ff20f40a875-luxury-private-lobby-in-a-hotel-entrance-and-rece-2024-10-18-17-47-36-utc.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/d46669a5-34c2-4f67-a4b5-e6fcdd63cec6-modern-hotel-lobby-with-abstract-wooden-wall-2025-03-18-18-36-30-utc.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/dfdfc5c3-7e3d-4d59-8330-7373fbff4da3-modern-interior-wooden-wall-2024-12-02-10-07-48-utc.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/1fe62cb1-5f64-45b5-83c7-c4544113bb11-modern-restaurant-interior-2024-10-12-14-05-47-utc.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/1336bb07-06ad-47e6-a7a2-6ba6d6ca43ff-nobody-in-messy-living-room-space-with-leftovers-f-2025-02-17-05-39-41-utc.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/050d8b43-1f9c-4b75-8b7b-681c0d059147-nominated-already-2025-02-25-03-58-29-utc.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/a7eac355-e835-48c7-b066-948eade20441-room-design-2024-10-18-08-23-47-utc.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/c1003ea6-1c81-4ba7-b117-11e1718a5203-the-scandinavian-style-interior-of-the-dining-room-2025-03-08-14-13-13-utc.jpg",
  ],
  spa: [
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/d0a69638-f974-4c6c-b3b5-bbe6966f303c-16fe53d84e2adb6433e0e2a6be772579.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/f43f747c-8f5f-4b92-888a-2df19a69d1a2-17a8645d4237e8f00a5e413ba39790d3.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/e9473996-ad5d-44cf-a992-ce6e7f22f1b3-80f01844b62ac0eb4937401d883801f3.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/0bfb124e-c3c9-47e9-91e1-60e5aa00f9dc-2213c8efdcb9fa36ce696047deb2fe85.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/a8904d31-210c-4806-a40e-ff4ba9a4fe2c-397658e036b1fb9bd456ce726c6f394c.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/70a948d8-f649-4e19-a298-96bd3dbf25bd-71216056b175d769b9863655d787813b.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/56fcbfe9-cb6b-46b2-b118-6e8525a3f7e3-739512143f4cdc54e67a7235ca4a8f77.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/a0e8610b-1d19-420f-827e-251dfd679cc0-04180461652e06ba26472bd8afceabae.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/ecbf5d57-2e1f-4f93-80b4-b4e780114842-aba1f62d22061cad9e318c4a0e38a3b9.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/4977f3f2-d06e-4ff1-b4b0-87785aa17cff-b270b6327664cf35a907a82fb7e555f5.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/d0ff4659-e24c-4239-a58e-7c427528d246-bb5835f8cd5e356b2a8b4499dfc77a47.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/93eff4e0-27b4-4ba6-8b18-64a8b175e61b-e29ca9377a57c5f96590b8b15b8ca0c8.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/bb3af139-5962-4f8f-82f8-8c756f0055dc-ffc2198e4b06d0ff8ba12b2d5bb7f960.jpg"
  ],
  shower: [
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/a55cfd5c-40df-4b7a-bb03-d1e7be15a499-204ee8184a9a677d7642a42b9c7bdc1e.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/f017cce2-9462-461b-9c70-f324b21e3595-7898ef4ec437956205a1140b47cceaaf.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/370f06fe-4bbd-4094-a057-ebd6db2b9aed-Gemini_Generated_Image_y91t9dy91t9dy91t.png",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/c2a2ba8f-af4f-4845-9139-683d09c10c06-gettyimages-168324184-612x612.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/e26f95f9-826c-4aae-ad5e-b43426bb6da5-gettyimages-1133604763-612x612.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/ad3880e4-489b-44a1-8f6b-7b7b8b489efd-gettyimages-1176677912-612x612.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/fd782cab-b8c1-43a5-9922-21a6c66ef82e-gettyimages-1284025718-612x612.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/14771b56-a718-4a71-a67a-74e9e17ea38a-gettyimages-1304826235-612x612.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/493d4e43-1bdb-456a-ac03-4a696dff9b84-gettyimages-1317170725-612x612.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/0ba4afa7-8147-493c-870f-f4429fd16408-gettyimages-1346631479-612x612.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/1ce755a6-ddfb-4d62-8b1d-cf7f33a76029-gettyimages-1437762451-612x612.jpg"
  ],
  workspace: [
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/1c5c3e3b-1739-45ca-9450-7fffabbae0c5-2ad1f891930eb3dde1e733349d29da97.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/17b7c366-0695-4c49-ba3c-c86bc854237d-95dd5e6715cf6e0194fb34f2134841da.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/e6cad7fe-1460-4638-b649-21d3e8a1570b-700f715aa3043a4f489a6f8904cc5339.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/8a150605-4cec-4bbb-a013-b2b977aeb718-28002f3128907b4f1a5e275272c78e9f.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/a9f1e6cf-34a8-45a3-9911-b7435540f9ee-212965a3274500be9be5c2f3a227f9de.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/e35a1afa-3429-456b-bb4c-b77e20da8bfc-649029891d31133b4aedb95d8d2ba20b.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/8e51f714-3d45-45dd-9187-35959c359526-a6ebff667764158167434eab31b3d36e.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/2aca2fe0-c80f-409f-8c2a-f14bec604079-da7dd02f26b8a995e7ba5245727de169.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/00cae7c8-fb9a-4bee-be39-6481d68351c6-e9f9974a5843dfdee4a34fa0710ec038.jpg",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/f6dc1327-ff4e-4855-ac8b-766f3604086d-e38bd6a0579ceb3d9acdfed7b9150c8f.jpg"
  ],
  capsule: [
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/fc7863db-2b8b-446e-bfe4-79dcacce29f4-Gemini_Generated_Image_3gx11r3gx11r3gx1.png",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/5eef9e5f-50b3-467f-8479-1a74bc62289a-Gemini_Generated_Image_3znlss3znlss3znl.png",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/379d5757-1685-42f6-8fe3-0b12a93e9337-Gemini_Generated_Image_4owoxf4owoxf4owo.png",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/aa6f81f4-5e44-4745-bd5b-3cfb5c8c89fd-Gemini_Generated_Image_i0i20zi0i20zi0i2.png",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/0b263614-cedb-4152-bf39-2fa3a694969b-Gemini_Generated_Image_ox226pox226pox22.png",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/0c331843-3c96-440c-97ad-09a225a1c474-Gemini_Generated_Image_raj4bjraj4bjraj4.png",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/64695919-7c34-4b00-90d6-42ff78c4fa16-Gemini_Generated_Image_rslln6rslln6rsll.png",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/f1d7f2bc-1e75-402c-b691-ddb41fcc6cb2-Gemini_Generated_Image_tu20d6tu20d6tu20.png",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/ce594cfa-3e7c-41b1-a4fd-67de5655fcb8-Gemini_Generated_Image_uycvufuycvufuycv.png",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/ee3ef284-16b0-4a03-8cfa-347a701ac848-Gemini_Generated_Image_y0ukmry0ukmry0uk.png",
    "https://elaachi-master-files.s3.us-east-1.amazonaws.com/10b9b7e4-b2a1-4873-ae04-42076c4dbb03-Gemini_Generated_Image_yjwedvyjwedvyjwe.png"
  ]

};

// 🔹 Map serviceType ObjectIds to keys
const serviceTypeMap: Record<string, keyof typeof imageMap> = {
  '68b7d200cdbe3594d70a9c95': 'hotel',
  '68b7da3dcdbe3594d70a9f4f': 'spa',
  '68b7da4ecdbe3594d70a9f59': 'capsule',
  '68b7da5dcdbe3594d70a9f5e': 'shower',
  '68c128acee83e4a64226f653': 'lounge',
  '68b7da46cdbe3594d70a9f54': 'workspace',
};

// 🔹 Utility to pick random N images
function getRandomImages(images: string[], count = 3): string[] {
  const shuffled = [...images].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
}

async function run() {
  await mongoose.connect(MONGO_URI);
  console.log('✅ Connected to MongoDB');
  const propertiesCount = await PropertyModel.countDocuments({});
  const domainValuesCount = await DomainValueModel.countDocuments({});
  console.log("🐞propertiesCount, domainValuesCount: ", propertiesCount, domainValuesCount);

  const properties = await PropertyDetailsModel.find<IPropertyDetails>({}).populate<IPropertyDetails & { propertyId: IProperty }>([
    {
      path: 'propertyId',
    }
  ]);
  console.log(`🔍 Found ${properties.length} properties`);

  for (const property of properties) {
    console.log(`🔍 Found ${property.propertyId.name} ${property.propertyId.serviceType}`);
    const serviceTypeId = property.propertyId.serviceType?.toString();
    const serviceKey = serviceTypeMap[serviceTypeId];

    if (!serviceKey || !imageMap[serviceKey]) {
      console.log(`⚠️ No images for property ${property.propertyId._id}`);
      continue;
    }

    const randomImages = getRandomImages(
      imageMap[serviceKey],
      Math.floor(Math.random() * 2) + 3 // 3–4 images
    );

    property.attachments = randomImages;

    await property.save();
    console.log(
      `✅ Updated ${property._id} (${serviceKey}) with ${randomImages.length} images`
    );
  }

  await mongoose.disconnect();
  console.log('🔌 Disconnected');
}

run().catch((err) => {
  console.error(err);
  process.exit(1);
});
