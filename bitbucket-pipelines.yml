image: python:3.7.4-alpine3.10

options:
  docker: true
  size: 2x

definitions:
  services:
    docker:
      memory: 7128
  steps:
    - step: &build-dev
        size: 2x
        name: Build Dev
        caches:
          - node
        script:
          - pip3 install awscli
          - aws configure set aws_access_key_id "${AWS_ACCESS_KEY_ID}"
          - aws configure set aws_secret_access_key "${AWS_SECRET_ACCESS_KEY}"
          - eval $(aws ecr get-login --region us-east-1 --no-include-email)
          - docker build -t staytransit-backend .
          - docker tag staytransit-backend:latest 281793537961.dkr.ecr.us-east-1.amazonaws.com/staytransit-backend:latest
          - docker push 281793537961.dkr.ecr.us-east-1.amazonaws.com/staytransit-backend:latest
    - step: &deploy-dev
        name: Deploy Dev
        script:
          - echo "Deploying to Dev environment from main branch"
          - pipe: 'atlassian/ssh-run:0.8.1'
            variables:
              SSH_USER: root
              SERVER: $DEV_SERVER_IP
              SSH_KEY: $DEV_SSH_KEY
              COMMAND: |
                sudo aws ecr get-login-password --region us-east-1 | sudo docker login --username AWS --password-stdin 281793537961.dkr.ecr.us-east-1.amazonaws.com;
                sudo docker stop staytransit-backend || true;
                sudo docker rm staytransit-backend || true;
                sudo docker rmi 281793537961.dkr.ecr.us-east-1.amazonaws.com/staytransit-backend || true;
                sudo docker pull 281793537961.dkr.ecr.us-east-1.amazonaws.com/staytransit-backend;
                sudo docker run -d --name staytransit-backend -p 127.0.0.1:4002:5000 --restart always 281793537961.dkr.ecr.us-east-1.amazonaws.com/staytransit-backend;
                sudo docker system prune -f;

pipelines:
  branches:
    main:
      - step: *build-dev
      - step: *deploy-dev
